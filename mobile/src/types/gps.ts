/**
 * GPS and recording related type definitions
 */

/**
 * Represents a single GPS recording point
 */
export type RecordingPoint = {
  latitude: number;
  longitude: number;
  timestamp: number;
  accuracy?: number;
  altitude?: number;
  speed?: number;
  heading?: number;
};

/**
 * Represents a field boundary
 */
export type FieldBoundary = {
  id: string;
  name: string;
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
};

/**
 * Represents a GPS recording session
 */
export type GpsRecording = {
  id: string;
  name: string;
  fieldId?: string;
  fieldName?: string;
  recordingDate: string;
  implementWidth: number;
  gpsData: RecordingPoint[];
  duration: number;
  distance: number;
  area: number;
  avgSpeed: number;
  createdAt: string;
  updatedAt: string;
};

/**
 * Represents the status of an external GPS device
 */
export type GpsDeviceStatus = {
  connected: boolean;
  deviceName?: string;
  deviceId?: string;
  accuracy?: number;
  batteryLevel?: number;
  signalStrength?: number;
};

/**
 * Represents the recording status
 */
export enum RecordingStatus {
  IDLE = 'IDLE',
  RECORDING = 'RECORDING',
  PAUSED = 'PAUSED',
  SAVING = 'SAVING',
}

/**
 * Represents recording statistics
 */
export type RecordingStats = {
  duration: number;
  distance: number;
  area: number;
  avgSpeed: number;
};