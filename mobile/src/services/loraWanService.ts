import { Platform } from 'react-native';

// Types for LoRaWAN messages
export type LoRaWANMessage = {
  id: string;
  senderId: string;
  senderName: string;
  recipientId?: string; // Optional for broadcast messages
  content: string;
  timestamp: number;
  location?: {
    latitude: number;
    longitude: number;
  };
  messageType: 'direct' | 'broadcast' | 'relay';
  ttl?: number; // Time-to-live for relay messages (in hops)
  relayedBy?: string[]; // IDs of nodes that have relayed this message
};

// Types for nearby farmers
export type NearbyFarmer = {
  id: string;
  name: string;
  lastSeen: number;
  distance?: number; // Approximate distance in meters
  location?: {
    latitude: number;
    longitude: number;
  };
  signalStrength: number; // 0-100
};

/**
 * Service for handling LoRaWAN communication between farmers
 */
class LoRaWANService {
  private isInitialized = false;
  private deviceId: string = '';
  private userName: string = '';
  private onMessageCallback: ((message: LoRaWANMessage) => void) | null = null;
  private onNearbyFarmersChangeCallback: ((farmers: NearbyFarmer[]) => void) | null = null;
  private nearbyFarmers: NearbyFarmer[] = [];
  private messageHistory: LoRaWANMessage[] = [];
  private currentLocation: { latitude: number; longitude: number } | null = null;

  /**
   * Initialize the LoRaWAN service
   * @param userId User ID for this device
   * @param userName User name to display to other farmers
   * @returns Promise that resolves when initialization is complete
   */
  public async initialize(userId: string, userName: string): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log(`Initializing LoRaWAN service for user ${userName} (${userId})`);
    
    this.deviceId = userId;
    this.userName = userName;
    
    // Check if we're running on a device that supports LoRaWAN
    if (!this.isLoRaWANSupported()) {
      console.warn('LoRaWAN is not supported on this device. Running in simulation mode.');
      // Start simulation mode for development/testing
      this.startSimulation();
      this.isInitialized = true;
      return;
    }

    try {
      // In a real implementation, this would initialize the LoRaWAN hardware
      // For now, we'll just simulate it
      
      // Request necessary permissions
      if (Platform.OS === 'android') {
        await this.requestAndroidPermissions();
      }
      
      // Initialize LoRaWAN hardware
      // This would typically involve:
      // 1. Setting up the LoRaWAN radio
      // 2. Joining the LoRaWAN network
      // 3. Setting up message handlers
      
      // For now, we'll just simulate it
      this.startSimulation();
      
      this.isInitialized = true;
      console.log('LoRaWAN service initialized successfully');
    } catch (error) {
      console.error('Error initializing LoRaWAN service:', error);
      throw error;
    }
  }

  /**
   * Check if LoRaWAN is supported on this device
   * @returns True if supported, false otherwise
   */
  private isLoRaWANSupported(): boolean {
    // In a real implementation, this would check if the device has LoRaWAN hardware
    // For now, we'll just return false to force simulation mode
    return false;
  }

  /**
   * Request necessary Android permissions for LoRaWAN
   * @returns Promise that resolves when permissions are granted
   */
  private async requestAndroidPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      // Import the PermissionsAndroid module dynamically to avoid issues on iOS
      const { PermissionsAndroid } = require('react-native');

      // Request location permissions (needed for LoRaWAN in some implementations)
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location for LoRaWAN communication.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (error) {
      console.error('Error requesting Android permissions:', error);
      return false;
    }
  }

  /**
   * Start simulation mode for development/testing
   */
  private startSimulation(): void {
    console.log('Starting LoRaWAN simulation mode');
    
    // Simulate nearby farmers
    this.simulateNearbyFarmers();
    
    // Simulate incoming messages
    this.simulateIncomingMessages();
  }

  /**
   * Simulate nearby farmers for development/testing
   */
  private simulateNearbyFarmers(): void {
    // Create some simulated nearby farmers
    const simulatedFarmers: NearbyFarmer[] = [
      {
        id: 'farmer1',
        name: 'John Smith',
        lastSeen: Date.now(),
        distance: 1200, // 1.2 km
        location: {
          latitude: 37.7749,
          longitude: -122.4194
        },
        signalStrength: 85
      },
      {
        id: 'farmer2',
        name: 'Sarah Johnson',
        lastSeen: Date.now(),
        distance: 800, // 800 m
        location: {
          latitude: 37.7749,
          longitude: -122.4194
        },
        signalStrength: 92
      },
      {
        id: 'farmer3',
        name: 'Michael Brown',
        lastSeen: Date.now() - 300000, // 5 minutes ago
        distance: 2500, // 2.5 km
        location: {
          latitude: 37.7749,
          longitude: -122.4194
        },
        signalStrength: 65
      }
    ];
    
    this.nearbyFarmers = simulatedFarmers;
    
    // Notify listeners
    if (this.onNearbyFarmersChangeCallback) {
      this.onNearbyFarmersChangeCallback(this.nearbyFarmers);
    }
    
    // Update nearby farmers every 30 seconds
    setInterval(() => {
      // Update last seen times
      this.nearbyFarmers.forEach(farmer => {
        // 50% chance of updating the farmer's status
        if (Math.random() > 0.5) {
          farmer.lastSeen = Date.now();
          farmer.signalStrength = Math.min(100, Math.max(50, farmer.signalStrength + (Math.random() * 10 - 5)));
          farmer.distance = Math.max(100, farmer.distance + (Math.random() * 200 - 100));
        }
      });
      
      // Randomly add or remove a farmer
      if (Math.random() > 0.8) {
        if (this.nearbyFarmers.length < 5 && Math.random() > 0.5) {
          // Add a new farmer
          const newFarmer: NearbyFarmer = {
            id: `farmer${Math.floor(Math.random() * 1000)}`,
            name: `Farmer ${Math.floor(Math.random() * 100)}`,
            lastSeen: Date.now(),
            distance: Math.random() * 3000 + 500,
            location: {
              latitude: 37.7749 + (Math.random() * 0.02 - 0.01),
              longitude: -122.4194 + (Math.random() * 0.02 - 0.01)
            },
            signalStrength: Math.random() * 30 + 60
          };
          this.nearbyFarmers.push(newFarmer);
        } else if (this.nearbyFarmers.length > 0) {
          // Remove a farmer
          const indexToRemove = Math.floor(Math.random() * this.nearbyFarmers.length);
          this.nearbyFarmers.splice(indexToRemove, 1);
        }
      }
      
      // Notify listeners
      if (this.onNearbyFarmersChangeCallback) {
        this.onNearbyFarmersChangeCallback([...this.nearbyFarmers]);
      }
    }, 30000);
  }

  /**
   * Simulate incoming messages for development/testing
   */
  private simulateIncomingMessages(): void {
    // Simulate receiving a message every 1-3 minutes
    setInterval(() => {
      // Only simulate messages if we have nearby farmers
      if (this.nearbyFarmers.length === 0) {
        return;
      }
      
      // Select a random farmer
      const farmerIndex = Math.floor(Math.random() * this.nearbyFarmers.length);
      const farmer = this.nearbyFarmers[farmerIndex];
      
      // Create a simulated message
      const messageTypes: ('direct' | 'broadcast' | 'relay')[] = ['direct', 'broadcast', 'relay'];
      const messageType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
      
      const simulatedMessage: LoRaWANMessage = {
        id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        senderId: farmer.id,
        senderName: farmer.name,
        content: this.getRandomMessageContent(messageType),
        timestamp: Date.now(),
        location: farmer.location,
        messageType: messageType
      };
      
      // For direct messages, set the recipient to this device
      if (messageType === 'direct') {
        simulatedMessage.recipientId = this.deviceId;
      }
      
      // For relay messages, add TTL and relayed by information
      if (messageType === 'relay') {
        simulatedMessage.ttl = Math.floor(Math.random() * 5) + 1;
        simulatedMessage.relayedBy = [
          `farmer${Math.floor(Math.random() * 1000)}`,
          `farmer${Math.floor(Math.random() * 1000)}`
        ];
      }
      
      // Add to message history
      this.messageHistory.push(simulatedMessage);
      
      // Notify listeners
      if (this.onMessageCallback) {
        this.onMessageCallback(simulatedMessage);
      }
    }, Math.random() * 120000 + 60000); // 1-3 minutes
  }

  /**
   * Get random message content for simulation
   * @param messageType The type of message
   * @returns Random message content
   */
  private getRandomMessageContent(messageType: 'direct' | 'broadcast' | 'relay'): string {
    const directMessages = [
      "Hey, do you have any spare parts for a John Deere tractor?",
      "Can I borrow your sprayer tomorrow?",
      "What variety of corn did you plant this year?",
      "Have you seen any armyworms in your fields?",
      "Weather forecast says rain tomorrow. Are you delaying harvest?"
    ];
    
    const broadcastMessages = [
      "Anyone seeing drought stress in their western fields?",
      "Heads up! Spotted some corn borer activity in my north field.",
      "Grain prices at the co-op just went up!",
      "Anyone interested in sharing equipment for fall tillage?",
      "County road 42 is closed due to flooding."
    ];
    
    const relayMessages = [
      "Emergency: Tractor overturned on County Road 7. Need assistance.",
      "Fire reported near Johnson's farm. Be alert.",
      "Severe weather warning for our county in the next 2 hours.",
      "Chemical spill on Highway 18. Avoid the area.",
      "Lost cattle reported near the river. Please keep an eye out."
    ];
    
    switch (messageType) {
      case 'direct':
        return directMessages[Math.floor(Math.random() * directMessages.length)];
      case 'broadcast':
        return broadcastMessages[Math.floor(Math.random() * broadcastMessages.length)];
      case 'relay':
        return relayMessages[Math.floor(Math.random() * relayMessages.length)];
      default:
        return "Hello from a nearby farmer!";
    }
  }

  /**
   * Set callback for receiving messages
   * @param callback Function to call when a message is received
   */
  public onMessage(callback: (message: LoRaWANMessage) => void): void {
    this.onMessageCallback = callback;
  }

  /**
   * Set callback for nearby farmers changes
   * @param callback Function to call when nearby farmers change
   */
  public onNearbyFarmersChange(callback: (farmers: NearbyFarmer[]) => void): void {
    this.onNearbyFarmersChangeCallback = callback;
    
    // Immediately call with current farmers if available
    if (this.nearbyFarmers.length > 0) {
      callback([...this.nearbyFarmers]);
    }
  }

  /**
   * Update the current location
   * @param latitude Latitude in degrees
   * @param longitude Longitude in degrees
   */
  public updateLocation(latitude: number, longitude: number): void {
    this.currentLocation = { latitude, longitude };
    
    // In a real implementation, this would update the location in the LoRaWAN device
    console.log(`Updated location: ${latitude}, ${longitude}`);
  }

  /**
   * Send a direct message to a specific farmer
   * @param recipientId ID of the recipient
   * @param content Message content
   * @returns Promise that resolves with the sent message
   */
  public async sendDirectMessage(recipientId: string, content: string): Promise<LoRaWANMessage> {
    if (!this.isInitialized) {
      throw new Error('LoRaWAN service not initialized');
    }
    
    // Create the message
    const message: LoRaWANMessage = {
      id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      senderId: this.deviceId,
      senderName: this.userName,
      recipientId: recipientId,
      content: content,
      timestamp: Date.now(),
      location: this.currentLocation || undefined,
      messageType: 'direct'
    };
    
    // In a real implementation, this would send the message over LoRaWAN
    console.log(`Sending direct message to ${recipientId}: ${content}`);
    
    // Add to message history
    this.messageHistory.push(message);
    
    return message;
  }

  /**
   * Send a broadcast message to all nearby farmers
   * @param content Message content
   * @returns Promise that resolves with the sent message
   */
  public async sendBroadcastMessage(content: string): Promise<LoRaWANMessage> {
    if (!this.isInitialized) {
      throw new Error('LoRaWAN service not initialized');
    }
    
    // Create the message
    const message: LoRaWANMessage = {
      id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      senderId: this.deviceId,
      senderName: this.userName,
      content: content,
      timestamp: Date.now(),
      location: this.currentLocation || undefined,
      messageType: 'broadcast'
    };
    
    // In a real implementation, this would send the message over LoRaWAN
    console.log(`Sending broadcast message: ${content}`);
    
    // Add to message history
    this.messageHistory.push(message);
    
    return message;
  }

  /**
   * Send a relay message (will be forwarded by other nodes)
   * @param content Message content
   * @param ttl Time-to-live in hops (default: 3)
   * @returns Promise that resolves with the sent message
   */
  public async sendRelayMessage(content: string, ttl: number = 3): Promise<LoRaWANMessage> {
    if (!this.isInitialized) {
      throw new Error('LoRaWAN service not initialized');
    }
    
    // Create the message
    const message: LoRaWANMessage = {
      id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      senderId: this.deviceId,
      senderName: this.userName,
      content: content,
      timestamp: Date.now(),
      location: this.currentLocation || undefined,
      messageType: 'relay',
      ttl: ttl,
      relayedBy: []
    };
    
    // In a real implementation, this would send the message over LoRaWAN
    console.log(`Sending relay message (TTL=${ttl}): ${content}`);
    
    // Add to message history
    this.messageHistory.push(message);
    
    return message;
  }

  /**
   * Get all nearby farmers
   * @returns Array of nearby farmers
   */
  public getNearbyFarmers(): NearbyFarmer[] {
    return [...this.nearbyFarmers];
  }

  /**
   * Get message history
   * @returns Array of messages
   */
  public getMessageHistory(): LoRaWANMessage[] {
    return [...this.messageHistory];
  }

  /**
   * Clean up resources when the service is no longer needed
   */
  public destroy(): void {
    // In a real implementation, this would clean up LoRaWAN resources
    console.log('Destroying LoRaWAN service');
    
    // Clear callbacks
    this.onMessageCallback = null;
    this.onNearbyFarmersChangeCallback = null;
    
    this.isInitialized = false;
  }
}

// Export a singleton instance
export const loraWanService = new LoRaWANService();
export default loraWanService;