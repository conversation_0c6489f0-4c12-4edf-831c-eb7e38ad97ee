import * as SQLite from 'expo-sqlite';
import { Platform } from 'react-native';

/**
 * Database service for local storage
 * This service provides methods for creating, reading, updating, and deleting data in the local SQLite database
 */

// Open the database
const openDatabase = (): SQLite.WebSQLDatabase => {
  if (Platform.OS === 'web') {
    // Web doesn't support SQLite, so we'll use a polyfill or alternative approach
    // For now, we'll just throw an error
    throw new Error('SQLite is not supported on web');
  }
  return SQLite.openDatabase('nxtacre.db');
};

const db = openDatabase();

// Initialize the database by creating necessary tables
const initDatabase = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Create tables for offline data storage
    db.transaction(tx => {
      // Table for storing API requests that need to be synced
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS sync_queue (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          method TEXT NOT NULL,
          url TEXT NOT NULL,
          data TEXT,
          headers TEXT,
          created_at INTEGER NOT NULL
        )`,
        [],
        () => {
          console.log('Sync queue table created successfully');
        },
        (_, error) => {
          console.error('Error creating sync queue table:', error);
          return true; // Rollback the transaction
        }
      );

      // Table for storing fields data
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS fields (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          acres REAL,
          farm_id TEXT NOT NULL,
          geometry TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          data TEXT NOT NULL
        )`,
        [],
        () => {
          console.log('Fields table created successfully');
        },
        (_, error) => {
          console.error('Error creating fields table:', error);
          return true; // Rollback the transaction
        }
      );

      // Table for storing tasks data
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS tasks (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          status TEXT NOT NULL,
          priority TEXT NOT NULL,
          due_date INTEGER,
          assigned_to_id TEXT,
          field_id TEXT,
          equipment_id TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          data TEXT NOT NULL
        )`,
        [],
        () => {
          console.log('Tasks table created successfully');
        },
        (_, error) => {
          console.error('Error creating tasks table:', error);
          return true; // Rollback the transaction
        }
      );

      // Table for storing GPS recordings
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS gps_recordings (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          field_id TEXT,
          implement_width REAL NOT NULL,
          recording_date INTEGER NOT NULL,
          gps_data TEXT NOT NULL,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          synced INTEGER NOT NULL DEFAULT 0
        )`,
        [],
        () => {
          console.log('GPS recordings table created successfully');
          resolve();
        },
        (_, error) => {
          console.error('Error creating GPS recordings table:', error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Add an item to the sync queue
const addToSyncQueue = async (
  method: string,
  url: string,
  data?: any,
  headers?: any
): Promise<number> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `INSERT INTO sync_queue (method, url, data, headers, created_at) VALUES (?, ?, ?, ?, ?)`,
        [
          method,
          url,
          data ? JSON.stringify(data) : null,
          headers ? JSON.stringify(headers) : null,
          Date.now()
        ],
        (_, result) => {
          resolve(result.insertId || 0);
        },
        (_, error) => {
          console.error('Error adding to sync queue:', error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Get all items from the sync queue
const getSyncQueue = async (): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT * FROM sync_queue ORDER BY created_at ASC`,
        [],
        (_, result) => {
          const items: any[] = [];
          for (let i = 0; i < result.rows.length; i++) {
            const item = result.rows.item(i);
            items.push({
              id: item.id,
              method: item.method,
              url: item.url,
              data: item.data ? JSON.parse(item.data) : null,
              headers: item.headers ? JSON.parse(item.headers) : null,
              createdAt: item.created_at
            });
          }
          resolve(items);
        },
        (_, error) => {
          console.error('Error getting sync queue:', error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Remove an item from the sync queue
const removeFromSyncQueue = async (id: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `DELETE FROM sync_queue WHERE id = ?`,
        [id],
        () => {
          resolve();
        },
        (_, error) => {
          console.error('Error removing from sync queue:', error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Generic function to save an entity to the local database
const saveEntity = async <T extends { id: string }>(
  tableName: string,
  entity: T,
  additionalFields: Record<string, any> = {}
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    const columns = ['id', 'created_at', 'updated_at', 'data', ...Object.keys(additionalFields)];
    const placeholders = columns.map(() => '?').join(', ');
    const values = [
      entity.id,
      now,
      now,
      JSON.stringify(entity),
      ...Object.values(additionalFields)
    ];

    db.transaction(tx => {
      tx.executeSql(
        `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`,
        values,
        () => {
          resolve();
        },
        (_, error) => {
          console.error(`Error saving entity to ${tableName}:`, error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Generic function to get entities from the local database
const getEntities = async <T>(tableName: string, whereClause?: string, params?: any[]): Promise<T[]> => {
  return new Promise((resolve, reject) => {
    const query = `SELECT * FROM ${tableName}${whereClause ? ` WHERE ${whereClause}` : ''}`;
    
    db.transaction(tx => {
      tx.executeSql(
        query,
        params || [],
        (_, result) => {
          const entities: T[] = [];
          for (let i = 0; i < result.rows.length; i++) {
            const item = result.rows.item(i);
            entities.push(JSON.parse(item.data));
          }
          resolve(entities);
        },
        (_, error) => {
          console.error(`Error getting entities from ${tableName}:`, error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Generic function to get a single entity from the local database
const getEntity = async <T>(tableName: string, id: string): Promise<T | null> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT * FROM ${tableName} WHERE id = ?`,
        [id],
        (_, result) => {
          if (result.rows.length > 0) {
            resolve(JSON.parse(result.rows.item(0).data));
          } else {
            resolve(null);
          }
        },
        (_, error) => {
          console.error(`Error getting entity from ${tableName}:`, error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Generic function to delete an entity from the local database
const deleteEntity = async (tableName: string, id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `DELETE FROM ${tableName} WHERE id = ?`,
        [id],
        () => {
          resolve();
        },
        (_, error) => {
          console.error(`Error deleting entity from ${tableName}:`, error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Specific functions for fields
const saveField = async (field: any): Promise<void> => {
  return saveEntity('fields', field, {
    name: field.name,
    acres: field.acres || null,
    farm_id: field.farmId,
    geometry: field.geometry ? JSON.stringify(field.geometry) : null
  });
};

const getFields = async (): Promise<any[]> => {
  return getEntities('fields');
};

const getField = async (id: string): Promise<any | null> => {
  return getEntity('fields', id);
};

const deleteField = async (id: string): Promise<void> => {
  return deleteEntity('fields', id);
};

// Specific functions for tasks
const saveTask = async (task: any): Promise<void> => {
  return saveEntity('tasks', task, {
    title: task.title,
    description: task.description || null,
    status: task.status,
    priority: task.priority,
    due_date: task.dueDate ? new Date(task.dueDate).getTime() : null,
    assigned_to_id: task.assignedToId || null,
    field_id: task.fieldId || null,
    equipment_id: task.equipmentId || null
  });
};

const getTasks = async (): Promise<any[]> => {
  return getEntities('tasks');
};

const getTask = async (id: string): Promise<any | null> => {
  return getEntity('tasks', id);
};

const deleteTask = async (id: string): Promise<void> => {
  return deleteEntity('tasks', id);
};

// Specific functions for GPS recordings
const saveGpsRecording = async (recording: any): Promise<void> => {
  return saveEntity('gps_recordings', recording, {
    name: recording.name,
    field_id: recording.fieldId || null,
    implement_width: recording.implementWidth,
    recording_date: new Date(recording.recordingDate).getTime(),
    gps_data: JSON.stringify(recording.gpsData),
    synced: recording.synced ? 1 : 0
  });
};

const getGpsRecordings = async (): Promise<any[]> => {
  return getEntities('gps_recordings');
};

const getGpsRecording = async (id: string): Promise<any | null> => {
  return getEntity('gps_recordings', id);
};

const deleteGpsRecording = async (id: string): Promise<void> => {
  return deleteEntity('gps_recordings', id);
};

const getUnsyncedGpsRecordings = async (): Promise<any[]> => {
  return getEntities('gps_recordings', 'synced = 0');
};

const markGpsRecordingAsSynced = async (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `UPDATE gps_recordings SET synced = 1 WHERE id = ?`,
        [id],
        () => {
          resolve();
        },
        (_, error) => {
          console.error('Error marking GPS recording as synced:', error);
          reject(error);
          return true; // Rollback the transaction
        }
      );
    });
  });
};

// Export the database service
const databaseService = {
  initDatabase,
  addToSyncQueue,
  getSyncQueue,
  removeFromSyncQueue,
  saveField,
  getFields,
  getField,
  deleteField,
  saveTask,
  getTasks,
  getTask,
  deleteTask,
  saveGpsRecording,
  getGpsRecordings,
  getGpsRecording,
  deleteGpsRecording,
  getUnsyncedGpsRecordings,
  markGpsRecordingAsSynced
};

export default databaseService;