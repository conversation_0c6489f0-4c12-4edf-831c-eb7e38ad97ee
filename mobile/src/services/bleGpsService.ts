import { Platform, NativeEventEmitter, NativeModules } from 'react-native';
import { GpsDeviceStatus } from '@/types/gps';
import Constants from 'expo-constants';

// Check if running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Conditionally import BLE modules
let BleManager, Device, Characteristic, State, BleManagerModule;
if (!isExpoGo) {
  try {
    // Only import if not running in Expo Go
    const BleModule = require('react-native-ble-plx');
    BleManager = BleModule.BleManager;
    Device = BleModule.Device;
    Characteristic = BleModule.Characteristic;
    State = BleModule.State;
    BleManagerModule = BleModule.BleManagerModule;
  } catch (error) {
    console.log('BLE module not available:', error);
  }
}

// GPS NMEA service and characteristic UUIDs
// These are example UUIDs and would need to be replaced with actual UUIDs for your GPS device
const GPS_SERVICE_UUID = '00001819-0000-1000-8000-00805f9b34fb'; // Example UUID for Location and Navigation service
const GPS_CHARACTERISTIC_UUID = '00002a67-0000-1000-8000-00805f9b34fb'; // Example UUID for Location and Speed characteristic

// RTK GPS service and characteristic UUIDs
// These are for high-precision RTK GPS devices
const RTK_GPS_SERVICE_UUID = '00001820-0000-1000-8000-00805f9b34fb'; // UUID for RTK GPS service
const RTK_GPS_CHARACTERISTIC_UUID = '00002a68-0000-1000-8000-00805f9b34fb'; // UUID for RTK GPS data

// ESP32 GPS service and characteristic UUIDs
// These are for custom ESP32-based GPS devices
const ESP32_GPS_SERVICE_UUID = '6E400001-B5A3-F393-E0A9-E50E24DCCA9E'; // UUID for ESP32 UART service
const ESP32_GPS_RX_CHARACTERISTIC_UUID = '6E400002-B5A3-F393-E0A9-E50E24DCCA9E'; // UUID for ESP32 RX characteristic
const ESP32_GPS_TX_CHARACTERISTIC_UUID = '6E400003-B5A3-F393-E0A9-E50E24DCCA9E'; // UUID for ESP32 TX characteristic

// GPS device types
enum GpsDeviceType {
  STANDARD = 'standard',
  RTK = 'rtk',
  ESP32 = 'esp32'
}

class BleGpsService {
  private manager: any;
  private device: any | null = null;
  private deviceType: GpsDeviceType = GpsDeviceType.STANDARD;
  private isScanning = false;
  private isConnected = false;
  private listeners: Array<() => void> = [];
  private onStatusChangeCallback: ((status: GpsDeviceStatus) => void) | null = null;
  private onLocationUpdateCallback: ((latitude: number, longitude: number, accuracy?: number) => void) | null = null;
  private lastKnownAccuracy: number = 5.0; // Default accuracy in meters

  constructor() {
    if (isExpoGo) {
      console.log('Running in Expo Go - BLE functionality is disabled');
      this.manager = null;
    } else if (BleManager) {
      try {
        this.manager = new BleManager();

        // Listen for state changes
        this.listeners.push(
          this.manager.onStateChange((state) => {
            if (state === State.PoweredOn) {
              // BLE is powered on and ready
              console.log('Bluetooth is powered on');
            } else {
              // BLE is not available
              console.log('Bluetooth state:', state);
              this.notifyStatusChange({
                connected: false,
              });
            }
          }, true)
        );
      } catch (error) {
        console.error('Error initializing BleManager:', error);
        this.manager = null;
      }
    } else {
      console.log('BLE Manager is not available');
      this.manager = null;
    }
  }

  /**
   * Set callback for GPS device status changes
   * @param callback Function to call when device status changes
   */
  public onStatusChange(callback: (status: GpsDeviceStatus) => void): void {
    this.onStatusChangeCallback = callback;
  }

  /**
   * Set callback for location updates from the GPS device
   * @param callback Function to call when a new location is received
   */
  public onLocationUpdate(callback: (latitude: number, longitude: number, accuracy?: number) => void): void {
    this.onLocationUpdateCallback = callback;
  }

  /**
   * Notify status change to registered callback
   * @param status Current GPS device status
   */
  private notifyStatusChange(status: GpsDeviceStatus): void {
    if (this.onStatusChangeCallback) {
      this.onStatusChangeCallback(status);
    }
  }

  /**
   * Notify location update to registered callback
   * @param latitude Latitude in degrees
   * @param longitude Longitude in degrees
   * @param accuracy Accuracy in meters (optional)
   */
  private notifyLocationUpdate(latitude: number, longitude: number, accuracy?: number): void {
    if (this.onLocationUpdateCallback) {
      this.onLocationUpdateCallback(latitude, longitude, accuracy);
    }
  }

  /**
   * Start scanning for GPS devices
   * @param deviceType Optional parameter to specify which type of GPS device to scan for
   * @returns Promise that resolves when scanning starts
   */
  public async startScan(deviceType?: GpsDeviceType): Promise<void> {
    if (this.isScanning) {
      return;
    }

    // If running in Expo Go or BLE is not available, return mock data
    if (isExpoGo || !this.manager) {
      console.log('BLE scanning not available in Expo Go or BLE not supported');
      this.notifyStatusChange({ 
        connected: false,
        message: 'BLE functionality is not available in Expo Go. Please use a development build for BLE features.'
      });
      return;
    }

    // Check if we have necessary permissions on Android
    if (Platform.OS === 'android') {
      const granted = await this.requestAndroidPermissions();
      if (!granted) {
        throw new Error('Bluetooth permissions not granted');
      }
    }

    this.isScanning = true;
    this.notifyStatusChange({ connected: false });

    // Determine which service UUIDs to scan for based on device type
    let serviceUUIDs: string[] = [];

    if (!deviceType || deviceType === GpsDeviceType.STANDARD) {
      serviceUUIDs.push(GPS_SERVICE_UUID);
    }

    if (!deviceType || deviceType === GpsDeviceType.RTK) {
      serviceUUIDs.push(RTK_GPS_SERVICE_UUID);
    }

    if (!deviceType || deviceType === GpsDeviceType.ESP32) {
      serviceUUIDs.push(ESP32_GPS_SERVICE_UUID);
    }

    console.log(`Scanning for GPS devices with services: ${serviceUUIDs.join(', ')}`);

    // Start scanning for devices with the specified GPS services
    this.manager.startDeviceScan(serviceUUIDs, null, (error, device) => {
      if (error) {
        console.error('Error scanning for devices:', error);
        this.isScanning = false;
        this.notifyStatusChange({ connected: false });
        return;
      }

      if (device) {
        console.log('Found device:', device.name || 'Unknown device', device.id);

        // Determine device type based on advertised services
        let detectedDeviceType = GpsDeviceType.STANDARD;

        // Check device name and advertised services to determine type
        // This is a simplified approach - in a real app, you might need more sophisticated detection
        if (device.name?.includes('RTK') || device.name?.includes('GNSS')) {
          detectedDeviceType = GpsDeviceType.RTK;
        } else if (device.name?.includes('ESP32') || device.name?.includes('ESP')) {
          detectedDeviceType = GpsDeviceType.ESP32;
        }

        // Stop scanning once we find a device
        this.manager.stopDeviceScan();
        this.isScanning = false;

        // Connect to the device with the detected type
        this.connectToDevice(device, detectedDeviceType);
      }
    });

    // Stop scanning after 15 seconds if no device is found (increased from 10 to allow for more device types)
    setTimeout(() => {
      if (this.isScanning) {
        this.manager.stopDeviceScan();
        this.isScanning = false;
        console.log('Scan timeout - no GPS devices found');
        this.notifyStatusChange({ connected: false });
      }
    }, 15000);
  }

  /**
   * Connect to a BLE GPS device
   * @param device The device to connect to
   * @param deviceType The type of GPS device
   */
  private async connectToDevice(device: any, deviceType: GpsDeviceType = GpsDeviceType.STANDARD): Promise<void> {
    // If running in Expo Go or BLE is not available, return
    if (isExpoGo || !this.manager) {
      console.log('BLE connection not available in Expo Go or BLE not supported');
      this.notifyStatusChange({ 
        connected: false,
        message: 'BLE functionality is not available in Expo Go. Please use a development build for BLE features.'
      });
      return;
    }

    try {
      console.log(`Connecting to ${deviceType} device:`, device.name || 'Unknown device');

      // Store the device type
      this.deviceType = deviceType;

      // Connect to the device
      const connectedDevice = await device.connect();
      console.log('Connected to device');

      // Discover services and characteristics
      const discoveredDevice = await connectedDevice.discoverAllServicesAndCharacteristics();
      console.log('Discovered services and characteristics');

      // Store the connected device
      this.device = discoveredDevice;
      this.isConnected = true;

      // Set initial accuracy based on device type
      let initialAccuracy = 5.0; // Default for standard GPS
      if (deviceType === GpsDeviceType.RTK) {
        initialAccuracy = 0.02; // 2cm accuracy for RTK GPS
      } else if (deviceType === GpsDeviceType.ESP32) {
        initialAccuracy = 2.0; // 2m accuracy for ESP32 (depends on the GPS module used)
      }
      this.lastKnownAccuracy = initialAccuracy;

      // Notify status change with device type and accuracy information
      this.notifyStatusChange({
        connected: true,
        deviceName: device.name || `Unknown ${deviceType} GPS device`,
        deviceId: device.id,
        accuracy: initialAccuracy,
        signalStrength: 100, // Initial value, will be updated later
      });

      // Subscribe to location updates based on device type
      this.subscribeToLocationUpdates();

      // Monitor disconnection
      discoveredDevice.onDisconnected((error, disconnectedDevice) => {
        console.log('Device disconnected:', disconnectedDevice.name || 'Unknown device');
        this.isConnected = false;
        this.device = null;
        this.notifyStatusChange({ connected: false });
      });
    } catch (error) {
      console.error('Error connecting to device:', error);
      this.isConnected = false;
      this.device = null;
      this.notifyStatusChange({ connected: false });
    }
  }

  /**
   * Subscribe to location updates from the GPS device
   */
  private async subscribeToLocationUpdates(): Promise<void> {
    // If running in Expo Go or BLE is not available, return
    if (isExpoGo || !this.manager) {
      console.log('BLE subscription not available in Expo Go or BLE not supported');
      return;
    }

    if (!this.device || !this.isConnected) {
      console.error('Cannot subscribe to location updates: device not connected');
      return;
    }

    try {
      // Subscribe to the appropriate characteristic based on device type
      switch (this.deviceType) {
        case GpsDeviceType.RTK:
          console.log('Subscribing to RTK GPS updates');
          this.device.monitorCharacteristicForService(
            RTK_GPS_SERVICE_UUID,
            RTK_GPS_CHARACTERISTIC_UUID,
            (error, characteristic) => {
              if (error) {
                console.error('Error monitoring RTK GPS characteristic:', error);
                return;
              }

              if (characteristic?.value) {
                // Parse the NMEA data from the characteristic value
                const nmeaData = this.parseNmeaData(characteristic);
                if (nmeaData) {
                  // RTK GPS provides very high accuracy
                  this.lastKnownAccuracy = nmeaData.accuracy || 0.02; // Default to 2cm if not provided
                  this.notifyLocationUpdate(
                    nmeaData.latitude,
                    nmeaData.longitude,
                    this.lastKnownAccuracy
                  );

                  // Update status with current accuracy
                  this.notifyStatusChange({
                    connected: true,
                    deviceName: this.device?.name || 'Unknown RTK GPS device',
                    deviceId: this.device?.id,
                    accuracy: this.lastKnownAccuracy,
                    signalStrength: nmeaData.signalStrength || 100,
                  });
                }
              }
            }
          );
          break;

        case GpsDeviceType.ESP32:
          console.log('Subscribing to ESP32 GPS updates');
          // ESP32 uses a UART service with RX/TX characteristics
          this.device.monitorCharacteristicForService(
            ESP32_GPS_SERVICE_UUID,
            ESP32_GPS_TX_CHARACTERISTIC_UUID, // We read from the TX characteristic
            (error, characteristic) => {
              if (error) {
                console.error('Error monitoring ESP32 GPS characteristic:', error);
                return;
              }

              if (characteristic?.value) {
                // Parse the NMEA data from the characteristic value
                const nmeaData = this.parseNmeaData(characteristic);
                if (nmeaData) {
                  // Update last known accuracy
                  this.lastKnownAccuracy = nmeaData.accuracy || 2.0; // Default to 2m if not provided
                  this.notifyLocationUpdate(
                    nmeaData.latitude,
                    nmeaData.longitude,
                    this.lastKnownAccuracy
                  );

                  // Update status with current accuracy
                  this.notifyStatusChange({
                    connected: true,
                    deviceName: this.device?.name || 'Unknown ESP32 GPS device',
                    deviceId: this.device?.id,
                    accuracy: this.lastKnownAccuracy,
                    signalStrength: nmeaData.signalStrength || 100,
                  });
                }
              }
            }
          );
          break;

        case GpsDeviceType.STANDARD:
        default:
          console.log('Subscribing to standard GPS updates');
          this.device.monitorCharacteristicForService(
            GPS_SERVICE_UUID,
            GPS_CHARACTERISTIC_UUID,
            (error, characteristic) => {
              if (error) {
                console.error('Error monitoring GPS characteristic:', error);
                return;
              }

              if (characteristic?.value) {
                // Parse the NMEA data from the characteristic value
                const nmeaData = this.parseNmeaData(characteristic);
                if (nmeaData) {
                  // Update last known accuracy
                  this.lastKnownAccuracy = nmeaData.accuracy || 5.0; // Default to 5m if not provided
                  this.notifyLocationUpdate(
                    nmeaData.latitude,
                    nmeaData.longitude,
                    this.lastKnownAccuracy
                  );

                  // Update status with current accuracy
                  this.notifyStatusChange({
                    connected: true,
                    deviceName: this.device?.name || 'Unknown GPS device',
                    deviceId: this.device?.id,
                    accuracy: this.lastKnownAccuracy,
                    signalStrength: nmeaData.signalStrength || 100,
                  });
                }
              }
            }
          );
          break;
      }
    } catch (error) {
      console.error('Error subscribing to location updates:', error);
    }
  }

  /**
   * Parse NMEA data from a BLE characteristic
   * @param characteristic The BLE characteristic containing NMEA data
   * @returns Parsed location data or null if parsing failed
   */
  private parseNmeaData(characteristic: any): { 
    latitude: number; 
    longitude: number; 
    accuracy?: number;
    signalStrength?: number;
    altitude?: number;
    speed?: number;
    heading?: number;
  } | null {
    // If running in Expo Go or BLE is not available, return null
    if (isExpoGo || !this.manager) {
      return null;
    }
    try {
      // Decode the base64 value to a string
      const base64Value = characteristic.value;
      if (!base64Value) {
        return null;
      }

      // Convert base64 to string
      const bytes = Buffer.from(base64Value, 'base64');
      const nmeaString = bytes.toString('utf-8').trim();

      // Check if it's a valid NMEA sentence
      if (!nmeaString.startsWith('$')) {
        console.log('Not a valid NMEA sentence:', nmeaString);
        return null;
      }

      // Parse different types of NMEA sentences
      // We support a wider range of NMEA sentences for better accuracy
      const sentenceType = nmeaString.substring(0, 6);

      switch (sentenceType) {
        case '$GPRMC':
        case '$GNRMC': // GNSS (multi-constellation) RMC
          return this.parseRMC(nmeaString);

        case '$GPGGA':
        case '$GNGGA': // GNSS GGA
          return this.parseGGA(nmeaString);

        case '$GPGSA':
        case '$GNGSA': // GNSS GSA (DOP and active satellites)
          return this.parseGSA(nmeaString);

        case '$GPGSV':
        case '$GNGSV': // GNSS GSV (Satellites in view)
          return this.parseGSV(nmeaString);

        case '$GPVTG':
        case '$GNVTG': // GNSS VTG (Track made good and ground speed)
          return this.parseVTG(nmeaString);

        case '$PTNL,': // Trimble proprietary messages (often used in RTK)
          if (nmeaString.startsWith('$PTNL,RTK')) {
            return this.parseTrimbleRTK(nmeaString);
          }
          return null;

        default:
          // Other sentence types can be added as needed
          console.log('Unsupported NMEA sentence type:', sentenceType);
          return null;
      }
    } catch (error) {
      console.error('Error parsing NMEA data:', error);
      return null;
    }
  }

  /**
   * Parse GPRMC/GNRMC (Recommended Minimum) NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseRMC(sentence: string): { 
    latitude: number; 
    longitude: number; 
    accuracy?: number;
    speed?: number;
    heading?: number;
  } | null {
    try {
      // $GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A
      // Format: $GPRMC,time,status,lat,lat-dir,lon,lon-dir,speed,angle,date,mag-var,mag-var-dir,checksum
      const parts = sentence.split(',');

      // Check if the sentence is valid (A=valid, V=invalid)
      if (parts[2] !== 'A') {
        return null;
      }

      // Parse latitude: DDMM.MMMM format
      const rawLat = parseFloat(parts[3]);
      const latDeg = Math.floor(rawLat / 100);
      const latMin = rawLat - (latDeg * 100);
      let latitude = latDeg + (latMin / 60);

      // Adjust for hemisphere (N/S)
      if (parts[4] === 'S') {
        latitude = -latitude;
      }

      // Parse longitude: DDDMM.MMMM format
      const rawLon = parseFloat(parts[5]);
      const lonDeg = Math.floor(rawLon / 100);
      const lonMin = rawLon - (lonDeg * 100);
      let longitude = lonDeg + (lonMin / 60);

      // Adjust for hemisphere (E/W)
      if (parts[6] === 'W') {
        longitude = -longitude;
      }

      // Parse speed (in knots) and convert to m/s
      const speedKnots = parseFloat(parts[7]);
      const speedMS = speedKnots * 0.514444; // Convert knots to m/s

      // Parse heading (true course in degrees)
      const heading = parseFloat(parts[8]);

      // Determine accuracy based on device type
      let accuracy: number;
      if (this.deviceType === GpsDeviceType.RTK) {
        accuracy = 0.02; // 2cm for RTK
      } else if (this.deviceType === GpsDeviceType.ESP32) {
        accuracy = 2.0; // 2m for ESP32
      } else {
        accuracy = 5.0; // 5m for standard GPS
      }

      return {
        latitude,
        longitude,
        accuracy,
        speed: speedMS,
        heading
      };
    } catch (error) {
      console.error('Error parsing RMC sentence:', error);
      return null;
    }
  }

  /**
   * Parse GPGGA/GNGGA (Global Positioning System Fix Data) NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseGGA(sentence: string): { 
    latitude: number; 
    longitude: number; 
    accuracy?: number;
    altitude?: number;
    signalStrength?: number;
  } | null {
    try {
      // $GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47
      // Format: $GPGGA,time,lat,lat-dir,lon,lon-dir,fix-quality,satellites,hdop,altitude,alt-unit,geoid-height,geoid-unit,age,station-id,checksum
      const parts = sentence.split(',');

      // Check if we have a valid fix (0=invalid, 1=GPS fix, 2=DGPS fix, 4=RTK fixed, 5=RTK float)
      const fixQuality = parseInt(parts[6]);
      if (fixQuality === 0) {
        return null;
      }

      // Parse latitude: DDMM.MMMM format
      const rawLat = parseFloat(parts[2]);
      const latDeg = Math.floor(rawLat / 100);
      const latMin = rawLat - (latDeg * 100);
      let latitude = latDeg + (latMin / 60);

      // Adjust for hemisphere (N/S)
      if (parts[3] === 'S') {
        latitude = -latitude;
      }

      // Parse longitude: DDDMM.MMMM format
      const rawLon = parseFloat(parts[4]);
      const lonDeg = Math.floor(rawLon / 100);
      const lonMin = rawLon - (lonDeg * 100);
      let longitude = lonDeg + (lonMin / 60);

      // Adjust for hemisphere (E/W)
      if (parts[5] === 'W') {
        longitude = -longitude;
      }

      // Parse number of satellites
      const satellites = parseInt(parts[7]);

      // HDOP (Horizontal Dilution of Precision) can be used to estimate accuracy
      const hdop = parseFloat(parts[8]);

      // Calculate accuracy based on fix quality, HDOP, and device type
      let accuracy: number;
      if (fixQuality === 4) { // RTK fixed
        accuracy = 0.02; // 2cm
      } else if (fixQuality === 5) { // RTK float
        accuracy = 0.2; // 20cm
      } else if (fixQuality === 2) { // DGPS
        accuracy = hdop * 1.0; // HDOP * 1m
      } else { // Standard GPS
        accuracy = hdop * 5.0; // HDOP * 5m
      }

      // Parse altitude
      const altitude = parseFloat(parts[9]);

      // Calculate signal strength based on number of satellites (0-100%)
      const signalStrength = Math.min(100, Math.round((satellites / 12) * 100));

      return {
        latitude,
        longitude,
        accuracy,
        altitude,
        signalStrength
      };
    } catch (error) {
      console.error('Error parsing GGA sentence:', error);
      return null;
    }
  }

  /**
   * Parse GPGSA/GNGSA (DOP and active satellites) NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseGSA(sentence: string): { 
    accuracy?: number;
    signalStrength?: number;
  } | null {
    try {
      // $GPGSA,A,3,04,05,09,12,,,,,,,,,1.0,0.8,0.7*3C
      // Format: $GPGSA,mode,fix-type,sat1,sat2,...,sat12,pdop,hdop,vdop,checksum
      const parts = sentence.split(',');

      // Check if we have a valid fix (1=no fix, 2=2D fix, 3=3D fix)
      const fixType = parseInt(parts[2]);
      if (fixType < 2) {
        return null;
      }

      // Count active satellites (non-empty fields from sat1 to sat12)
      let activeSatellites = 0;
      for (let i = 3; i <= 14; i++) {
        if (parts[i] && parts[i].trim() !== '') {
          activeSatellites++;
        }
      }

      // Parse DOP values (dilution of precision)
      const pdop = parseFloat(parts[15]); // Position DOP
      const hdop = parseFloat(parts[16]); // Horizontal DOP
      const vdop = parseFloat(parts[17].split('*')[0]); // Vertical DOP (remove checksum)

      // Calculate accuracy based on HDOP and device type
      let accuracy: number;
      if (this.deviceType === GpsDeviceType.RTK) {
        accuracy = 0.02; // 2cm for RTK
      } else if (this.deviceType === GpsDeviceType.ESP32) {
        accuracy = hdop * 1.0; // HDOP * 1m for ESP32
      } else {
        accuracy = hdop * 5.0; // HDOP * 5m for standard GPS
      }

      // Calculate signal strength based on number of active satellites (0-100%)
      const signalStrength = Math.min(100, Math.round((activeSatellites / 12) * 100));

      return {
        accuracy,
        signalStrength,
        // We don't have lat/lon in GSA sentences, so we return only accuracy and signal strength
        latitude: 0,
        longitude: 0
      };
    } catch (error) {
      console.error('Error parsing GSA sentence:', error);
      return null;
    }
  }

  /**
   * Parse GPGSV/GNGSV (Satellites in view) NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseGSV(sentence: string): { 
    signalStrength?: number;
  } | null {
    try {
      // $GPGSV,3,1,11,03,03,111,00,04,15,270,00,06,01,010,00,13,06,292,00*74
      // Format: $GPGSV,total-msgs,msg-num,sats-in-view,sat-id,elevation,azimuth,snr,...,checksum
      const parts = sentence.split(',');

      // Get total number of satellites in view
      const satsInView = parseInt(parts[3]);

      // Calculate signal strength based on number of satellites in view (0-100%)
      const signalStrength = Math.min(100, Math.round((satsInView / 12) * 100));

      return {
        signalStrength,
        // We don't have lat/lon in GSV sentences, so we return only signal strength
        latitude: 0,
        longitude: 0
      };
    } catch (error) {
      console.error('Error parsing GSV sentence:', error);
      return null;
    }
  }

  /**
   * Parse GPVTG/GNVTG (Track made good and ground speed) NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseVTG(sentence: string): { 
    speed?: number;
    heading?: number;
  } | null {
    try {
      // $GPVTG,054.7,T,034.4,M,005.5,N,010.2,K,A*2D
      // Format: $GPVTG,track-true,T,track-mag,M,speed-knots,N,speed-kph,K,mode,checksum
      const parts = sentence.split(',');

      // Check if we have a valid mode (A=autonomous, D=differential, E=estimated, N=not valid)
      const mode = parts[9].split('*')[0]; // Remove checksum
      if (mode === 'N') {
        return null;
      }

      // Parse true track/heading (in degrees)
      const heading = parseFloat(parts[1]);

      // Parse speed (in km/h) and convert to m/s
      const speedKPH = parseFloat(parts[7]);
      const speedMS = speedKPH / 3.6; // Convert km/h to m/s

      return {
        speed: speedMS,
        heading,
        // We don't have lat/lon in VTG sentences, so we return only speed and heading
        latitude: 0,
        longitude: 0
      };
    } catch (error) {
      console.error('Error parsing VTG sentence:', error);
      return null;
    }
  }

  /**
   * Parse Trimble RTK proprietary NMEA sentence
   * @param sentence The NMEA sentence
   * @returns Parsed location data or null if parsing failed
   */
  private parseTrimbleRTK(sentence: string): { 
    latitude: number; 
    longitude: number; 
    accuracy?: number;
  } | null {
    try {
      // $PTNL,RTK,181845.00,021210,3,7,2,0.011,0.014,0.046,4,1.3,1.4*01
      // Format varies by Trimble receiver model, but generally includes position error estimates
      const parts = sentence.split(',');

      // This is a simplified implementation as Trimble formats vary
      // In a real implementation, you would need to handle specific Trimble formats

      // Extract horizontal and vertical precision if available
      // Assuming parts[7] is horizontal error in meters
      const horizontalError = parseFloat(parts[7]);

      // Use the horizontal error as accuracy, or default to 2cm for RTK
      const accuracy = isNaN(horizontalError) ? 0.02 : horizontalError;

      // We don't have lat/lon in this simplified implementation
      // In a real implementation, you would extract them from the message or combine with other messages

      return {
        latitude: 0, // Placeholder
        longitude: 0, // Placeholder
        accuracy
      };
    } catch (error) {
      console.error('Error parsing Trimble RTK sentence:', error);
      return null;
    }
  }

  /**
   * Disconnect from the currently connected GPS device
   */
  public async disconnect(): Promise<void> {
    // If running in Expo Go or BLE is not available, just update state
    if (isExpoGo || !this.manager) {
      this.isConnected = false;
      this.device = null;
      this.notifyStatusChange({ connected: false });
      return;
    }

    if (this.device && this.isConnected) {
      try {
        await this.device.cancelConnection();
        console.log('Disconnected from device');
      } catch (error) {
        console.error('Error disconnecting from device:', error);
      }
    }

    this.isConnected = false;
    this.device = null;
    this.notifyStatusChange({ connected: false });
  }

  /**
   * Check if a GPS device is currently connected
   * @returns True if connected, false otherwise
   */
  public isDeviceConnected(): boolean {
    // If running in Expo Go or BLE is not available, always return false
    if (isExpoGo || !this.manager) {
      return false;
    }
    return this.isConnected && this.device !== null;
  }

  /**
   * Get the current GPS device status
   * @returns Current GPS device status
   */
  public getDeviceStatus(): GpsDeviceStatus {
    // If running in Expo Go or BLE is not available, return not connected with message
    if (isExpoGo || !this.manager) {
      return { 
        connected: false,
        message: 'BLE functionality is not available in Expo Go. Please use a development build for BLE features.'
      };
    }

    if (!this.isConnected || !this.device) {
      return { connected: false };
    }

    return {
      connected: true,
      deviceName: this.device.name || 'Unknown GPS device',
      deviceId: this.device.id,
    };
  }

  /**
   * Request necessary Android permissions for BLE
   * @returns Promise that resolves to true if permissions are granted, false otherwise
   */
  private async requestAndroidPermissions(): Promise<boolean> {
    // If running in Expo Go or BLE is not available, return true to avoid permission requests
    if (isExpoGo || !this.manager) {
      return true;
    }

    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      // Import the PermissionsAndroid module dynamically to avoid issues on iOS
      const { PermissionsAndroid } = require('react-native');

      // Check Android version to determine which permissions to request
      const apiLevel = Platform.Version as number;

      if (apiLevel >= 31) { // Android 12+
        // For Android 12 and above, we need BLUETOOTH_SCAN and BLUETOOTH_CONNECT
        const scanGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          {
            title: 'Bluetooth Scan Permission',
            message: 'This app needs permission to scan for Bluetooth devices.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        const connectGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          {
            title: 'Bluetooth Connect Permission',
            message: 'This app needs permission to connect to Bluetooth devices.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        const fineLocationGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location for Bluetooth scanning.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        return (
          scanGranted === PermissionsAndroid.RESULTS.GRANTED &&
          connectGranted === PermissionsAndroid.RESULTS.GRANTED &&
          fineLocationGranted === PermissionsAndroid.RESULTS.GRANTED
        );
      } else {
        // For Android 6-11, we need ACCESS_FINE_LOCATION
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location for Bluetooth scanning.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
    } catch (error) {
      console.error('Error requesting Android permissions:', error);
      return false;
    }
  }

  /**
   * Clean up resources when the service is no longer needed
   */
  public destroy(): void {
    // Disconnect from any connected device
    this.disconnect();

    // Remove all listeners if not in Expo Go and manager exists
    if (!isExpoGo && this.manager) {
      this.listeners.forEach(removeListener => removeListener());
    }
    this.listeners = [];

    // Clear callbacks
    this.onStatusChangeCallback = null;
    this.onLocationUpdateCallback = null;
  }
}

// Export a singleton instance
export const bleGpsService = new BleGpsService();
export default bleGpsService;
