import axios from 'axios';
import * as Location from 'expo-location';

// Types for weather data
export interface CurrentWeather {
  temp: number;
  condition: string;
  icon: string;
}

export interface ForecastDay {
  day: string;
  high: number;
  low: number;
  condition: string;
  icon: string;
}

export interface WeatherData {
  current: CurrentWeather;
  forecast: ForecastDay[];
}

// OpenWeatherMap API key - in a real app, this would be stored in environment variables
// For this example, we'll use a placeholder - you'll need to replace this with a real API key
const API_KEY = 'YOUR_OPENWEATHERMAP_API_KEY';

/**
 * Get weather data for the current location
 * @returns Promise<WeatherData> Weather data for the current location
 */
export const getWeatherForCurrentLocation = async (): Promise<WeatherData> => {
  try {
    // Request permission to access location
    const { status } = await Location.requestForegroundPermissionsAsync();
    
    if (status !== 'granted') {
      throw new Error('Permission to access location was denied');
    }

    // Get current location
    const location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;

    // Get current weather data
    const currentResponse = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?lat=${latitude}&lon=${longitude}&units=imperial&appid=${API_KEY}`
    );

    // Get forecast data (5 day / 3 hour forecast)
    const forecastResponse = await axios.get(
      `https://api.openweathermap.org/data/2.5/forecast?lat=${latitude}&lon=${longitude}&units=imperial&appid=${API_KEY}`
    );

    // Process current weather data
    const current: CurrentWeather = {
      temp: Math.round(currentResponse.data.main.temp),
      condition: currentResponse.data.weather[0].main,
      icon: currentResponse.data.weather[0].icon,
    };

    // Process forecast data
    // Group forecast by day and get min/max temperatures
    const dailyForecasts: { [key: string]: { temps: number[]; conditions: string[]; icons: string[] } } = {};
    
    forecastResponse.data.list.forEach((item: any) => {
      const date = new Date(item.dt * 1000);
      const day = date.toLocaleDateString('en-US', { weekday: 'short' });
      
      if (!dailyForecasts[day]) {
        dailyForecasts[day] = { temps: [], conditions: [], icons: [] };
      }
      
      dailyForecasts[day].temps.push(item.main.temp);
      dailyForecasts[day].conditions.push(item.weather[0].main);
      dailyForecasts[day].icons.push(item.weather[0].icon);
    });

    // Convert to array and limit to 3 days
    const forecast: ForecastDay[] = Object.entries(dailyForecasts)
      .map(([day, data]) => {
        // Get most common condition for the day
        const conditionCounts: { [key: string]: number } = {};
        data.conditions.forEach(condition => {
          conditionCounts[condition] = (conditionCounts[condition] || 0) + 1;
        });
        const condition = Object.entries(conditionCounts)
          .sort((a, b) => b[1] - a[1])[0][0];
        
        // Get most common icon for the day
        const iconCounts: { [key: string]: number } = {};
        data.icons.forEach(icon => {
          iconCounts[icon] = (iconCounts[icon] || 0) + 1;
        });
        const icon = Object.entries(iconCounts)
          .sort((a, b) => b[1] - a[1])[0][0];
        
        return {
          day: day === new Date().toLocaleDateString('en-US', { weekday: 'short' }) ? 'Today' : day,
          high: Math.round(Math.max(...data.temps)),
          low: Math.round(Math.min(...data.temps)),
          condition,
          icon,
        };
      })
      .slice(0, 3);

    return { current, forecast };
  } catch (error) {
    console.error('Error fetching weather data:', error);
    
    // Return mock data as fallback
    return {
      current: { temp: 72, condition: 'Sunny', icon: '01d' },
      forecast: [
        { day: 'Today', high: 75, low: 62, condition: 'Sunny', icon: '01d' },
        { day: 'Tomorrow', high: 78, low: 64, condition: 'Partly Cloudy', icon: '02d' },
        { day: 'Wed', high: 80, low: 66, condition: 'Cloudy', icon: '03d' },
      ],
    };
  }
};

/**
 * Get the appropriate Ionicons name for a weather condition
 * @param condition The weather condition from the API
 * @param icon The weather icon code from the API
 * @returns The Ionicons name to use
 */
export const getWeatherIconName = (condition: string, icon: string): string => {
  // Check if it's day or night
  const isNight = icon.endsWith('n');
  
  // Map OpenWeatherMap conditions to Ionicons names
  switch (condition) {
    case 'Clear':
      return isNight ? 'moon' : 'sunny';
    case 'Clouds':
      if (icon === '02d' || icon === '02n') {
        return isNight ? 'partly-sunny-outline' : 'partly-sunny';
      }
      return 'cloud';
    case 'Rain':
    case 'Drizzle':
      return 'rainy';
    case 'Thunderstorm':
      return 'thunderstorm';
    case 'Snow':
      return 'snow';
    case 'Mist':
    case 'Smoke':
    case 'Haze':
    case 'Dust':
    case 'Fog':
      return 'water-outline';
    default:
      return isNight ? 'moon' : 'sunny';
  }
};