import api from './apiClient';
import { RecordingPoint } from '@/types/gps';

// Define types for the service
export type SaveRecordingParams = {
  name: string;
  fieldId?: string;
  implementWidth: number;
  recordingPath: RecordingPoint[];
  startTime: number;
  endTime: number;
  stats: {
    duration: number;
    distance: number;
    area: number;
    avgSpeed: number;
  };
};

export type SavedRecording = {
  id: string;
  name: string;
  fieldId?: string;
  implementWidth: number;
  recordingDate: string;
  gpsData: RecordingPoint[];
  duration: number;
  distance: number;
  area: number;
  avgSpeed: number;
  createdAt: string;
  updatedAt: string;
};

// GPS Recording Service
const gpsRecordingService = {
  /**
   * Save a GPS recording to the server
   * @param params Recording data to save
   * @returns Promise with the saved recording data
   */
  saveRecording: async (params: SaveRecordingParams): Promise<SavedRecording> => {
    try {
      const response = await api.post<SavedRecording>('/gps-recordings', params);
      return response.data;
    } catch (error) {
      console.error('Error saving GPS recording:', error);
      throw error;
    }
  },

  /**
   * Get all GPS recordings for the current user
   * @returns Promise with an array of recordings
   */
  getRecordings: async (): Promise<SavedRecording[]> => {
    try {
      const response = await api.get<SavedRecording[]>('/gps-recordings');
      return response.data;
    } catch (error) {
      console.error('Error fetching GPS recordings:', error);
      throw error;
    }
  },

  /**
   * Get a specific GPS recording by ID
   * @param id Recording ID
   * @returns Promise with the recording data
   */
  getRecordingById: async (id: string): Promise<SavedRecording> => {
    try {
      const response = await api.get<SavedRecording>(`/gps-recordings/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching GPS recording with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a GPS recording
   * @param id Recording ID to delete
   * @returns Promise with the deleted recording data
   */
  deleteRecording: async (id: string): Promise<void> => {
    try {
      await api.delete(`/gps-recordings/${id}`);
    } catch (error) {
      console.error(`Error deleting GPS recording with ID ${id}:`, error);
      throw error;
    }
  }
};

export default gpsRecordingService;