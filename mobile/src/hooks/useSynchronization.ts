import { useEffect, useState } from 'react';
import synchronizationService from '../services/synchronizationService';
import networkService from '../services/networkService';

/**
 * Hook for initializing and using the synchronization service
 * This hook initializes the synchronization service when the component mounts
 * and provides methods and state for working with synchronization
 */

export const useSynchronization = () => {
  // Track initialization status
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Track synchronization status
  const [isSynchronizing, setIsSynchronizing] = useState(false);
  
  // Track network status
  const networkStatus = networkService.useNetworkStatus();
  
  // Initialize the synchronization service when the component mounts
  useEffect(() => {
    const initSync = async () => {
      try {
        await synchronizationService.initialize();
        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing synchronization service:', error);
      }
    };
    
    initSync();
  }, []);
  
  // Function to manually trigger synchronization
  const synchronize = async () => {
    if (!networkStatus.isConnected) {
      console.log('Cannot synchronize: device is offline');
      return false;
    }
    
    setIsSynchronizing(true);
    try {
      await synchronizationService.forceSynchronize();
      return true;
    } catch (error) {
      console.error('Error during manual synchronization:', error);
      return false;
    } finally {
      setIsSynchronizing(false);
    }
  };
  
  // Return the hook state and methods
  return {
    isInitialized,
    isSynchronizing,
    isOnline: networkStatus.isConnected,
    networkStatus,
    synchronize,
  };
};

export default useSynchronization;