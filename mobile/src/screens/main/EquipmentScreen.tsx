import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { equipmentService, Equipment } from '@/services/equipmentService';

type EquipmentScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Equipment'>,
  NativeStackScreenProps<MainStackParamList>
>;

// Equipment type is imported from equipmentService

const EquipmentScreen: React.FC<EquipmentScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEquipment, setFilteredEquipment] = useState<Equipment[]>([]);

  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        setLoading(true);

        if (!user?.farmId) {
          console.error('No farm ID available');
          Alert.alert('Error', 'Could not load equipment. No farm ID available.');
          setLoading(false);
          return;
        }

        const equipmentData = await equipmentService.getEquipment(user.farmId);
        setEquipment(equipmentData);
        setFilteredEquipment(equipmentData);
      } catch (error) {
        console.error('Error fetching equipment:', error);
        Alert.alert('Error', 'Could not load equipment. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [user?.farmId]);

  useEffect(() => {
    if (searchQuery) {
      const filtered = equipment.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.make && item.make.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.model && item.model.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredEquipment(filtered);
    } else {
      setFilteredEquipment(equipment);
    }
  }, [searchQuery, equipment]);

  const handleAddEquipment = () => {
    navigation.navigate('EquipmentForm', {});
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '#22c55e';
      case 'INACTIVE':
        return '#9ca3af';
      case 'MAINTENANCE':
        return '#f59e0b';
      case 'RETIRED':
        return '#6b7280';
      default:
        return '#9ca3af';
    }
  };

  const getEquipmentIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tractor':
        return 'car-outline';
      case 'combine':
        return 'construct-outline';
      case 'sprayer':
        return 'water-outline';
      case 'truck':
        return 'truck-outline';
      default:
        return 'cog-outline';
    }
  };

  const renderEquipmentItem = ({ item }: { item: Equipment }) => (
    <TouchableOpacity
      style={styles.equipmentItem}
      onPress={() => navigation.navigate('EquipmentDetail', { equipmentId: item.id })}
    >
      <View style={styles.equipmentIcon}>
        <Ionicons 
          name={getEquipmentIcon(item.type)} 
          size={24} 
          color="#22c55e" 
        />
      </View>
      <View style={styles.equipmentInfo}>
        <Text style={styles.equipmentName}>{item.name}</Text>
        <Text style={styles.equipmentDetails}>
          {item.make} {item.model} {item.year ? `(${item.year})` : ''}
        </Text>
        <View style={styles.equipmentStatus}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getStatusColor(item.status) }
          ]} />
          <Text style={styles.statusText}>{item.status}</Text>
          {item.lastMaintenance && (
            <Text style={styles.lastMaintenance}>Last maintenance: {item.lastMaintenance}</Text>
          )}
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading equipment...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search equipment..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredEquipment.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="construct" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No equipment found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? "Try a different search term" 
              : "Add your first equipment to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredEquipment}
          renderItem={renderEquipmentItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity style={styles.addButton} onPress={handleAddEquipment}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  equipmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  equipmentIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  equipmentInfo: {
    flex: 1,
  },
  equipmentName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
  },
  equipmentDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  equipmentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  lastMaintenance: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default EquipmentScreen;
