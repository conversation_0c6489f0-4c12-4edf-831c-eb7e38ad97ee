import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import invoiceService, { InvoiceWithItems } from '@/services/invoiceService';

type InvoiceDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'InvoiceDetail'>;

const InvoiceDetailScreen: React.FC<InvoiceDetailScreenProps> = ({ route, navigation }) => {
  const { invoiceId } = route.params;
  const [invoice, setInvoice] = useState<InvoiceWithItems | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInvoice = async () => {
      setLoading(true);
      try {
        const invoiceData = await invoiceService.getInvoiceById(invoiceId);
        if (invoiceData) {
          setInvoice(invoiceData);
        } else {
          // If API call fails, show an error
          Alert.alert('Error', 'Failed to load invoice data');
        }
      } catch (error) {
        console.error('Error fetching invoice:', error);
        Alert.alert('Error', 'An error occurred while loading invoice data');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId]);

  const handleEditInvoice = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Invoice', 'Invoice editing functionality would be implemented here.');
  };

  const handleSendInvoice = () => {
    // In a real app, this would send the invoice to the customer
    Alert.alert('Send Invoice', 'Invoice sending functionality would be implemented here.');
  };

  const handleMarkAsPaid = () => {
    // In a real app, this would mark the invoice as paid
    Alert.alert('Mark as Paid', 'Invoice payment functionality would be implemented here.');
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '#9ca3af';
      case 'SENT':
        return '#3b82f6';
      case 'PAID':
        return '#22c55e';
      case 'OVERDUE':
        return '#ef4444';
      case 'CANCELLED':
        return '#6b7280';
      default:
        return '#9ca3af';
    }
  };

  const renderInvoiceItem = ({ item }: { item: InvoiceItem }) => (
    <View style={styles.invoiceItem}>
      <View style={styles.itemDetails}>
        <Text style={styles.itemDescription}>{item.description}</Text>
        <Text style={styles.itemQuantity}>
          {item.quantity} x {formatCurrency(item.unitPrice)}
        </Text>
      </View>
      <Text style={styles.itemAmount}>{formatCurrency(item.amount)}</Text>
    </View>
  );

  const renderActionButtons = () => {
    if (invoice?.status === 'DRAFT') {
      return (
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.editButton]} 
            onPress={handleEditInvoice}
          >
            <Ionicons name="create-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.actionButton, styles.sendButton]} 
            onPress={handleSendInvoice}
          >
            <Ionicons name="paper-plane-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Send</Text>
          </TouchableOpacity>
        </View>
      );
    } else if (invoice?.status === 'SENT' || invoice?.status === 'OVERDUE') {
      return (
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.editButton]} 
            onPress={handleEditInvoice}
          >
            <Ionicons name="create-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.actionButton, styles.paidButton]} 
            onPress={handleMarkAsPaid}
          >
            <Ionicons name="checkmark-circle-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Mark as Paid</Text>
          </TouchableOpacity>
        </View>
      );
    } else if (invoice?.status === 'PAID') {
      return (
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.viewButton]} 
            onPress={() => Alert.alert('View Receipt', 'Receipt viewing functionality would be implemented here.')}
          >
            <Ionicons name="document-text-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>View Receipt</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading invoice details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.invoiceNumber}>{invoice?.invoiceNumber}</Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(invoice?.status || '') }
          ]}>
            <Text style={styles.statusText}>{invoice?.status}</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={handleEditInvoice}>
          <Ionicons name="create-outline" size={24} color="#22c55e" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <Text style={styles.customerName}>{invoice?.customerName}</Text>
          {invoice?.customerEmail && (
            <Text style={styles.customerDetail}>{invoice.customerEmail}</Text>
          )}
          {invoice?.customerPhone && (
            <Text style={styles.customerDetail}>{invoice.customerPhone}</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Invoice Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Issue Date:</Text>
            <Text style={styles.detailValue}>{formatDate(invoice?.issueDate || '')}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Due Date:</Text>
            <Text style={styles.detailValue}>{formatDate(invoice?.dueDate || '')}</Text>
          </View>
          {invoice?.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Notes:</Text>
              <Text style={styles.notesText}>{invoice.notes}</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          <FlatList
            data={invoice?.items}
            renderItem={renderInvoiceItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
          />
          <View style={styles.totalsContainer}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Subtotal:</Text>
              <Text style={styles.totalValue}>{formatCurrency(invoice?.subtotal || 0)}</Text>
            </View>
            {(invoice?.taxAmount || 0) > 0 && (
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Tax ({invoice?.taxRate}%):</Text>
                <Text style={styles.totalValue}>{formatCurrency(invoice?.taxAmount || 0)}</Text>
              </View>
            )}
            <View style={[styles.totalRow, styles.grandTotalRow]}>
              <Text style={styles.grandTotalLabel}>Total:</Text>
              <Text style={styles.grandTotalValue}>{formatCurrency(invoice?.total || 0)}</Text>
            </View>
          </View>
        </View>

        {renderActionButtons()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flex: 1,
  },
  invoiceNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    margin: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  customerDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  notesContainer: {
    marginTop: 10,
  },
  notesLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    fontStyle: 'italic',
  },
  invoiceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  itemDetails: {
    flex: 1,
  },
  itemDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 3,
  },
  itemQuantity: {
    fontSize: 12,
    color: '#666',
  },
  itemAmount: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  totalsContainer: {
    marginTop: 10,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
  totalLabel: {
    fontSize: 14,
    color: '#666',
  },
  totalValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  grandTotalRow: {
    marginTop: 5,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  grandTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  grandTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#22c55e',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
    marginTop: 5,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
  },
  editButton: {
    backgroundColor: '#6b7280',
  },
  sendButton: {
    backgroundColor: '#3b82f6',
  },
  paidButton: {
    backgroundColor: '#22c55e',
  },
  viewButton: {
    backgroundColor: '#3b82f6',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 5,
  },
});

export default InvoiceDetailScreen;
