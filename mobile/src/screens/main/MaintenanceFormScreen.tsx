import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { Picker } from '@react-native-picker/picker';

type MaintenanceFormScreenProps = NativeStackScreenProps<MainStackParamList, 'MaintenanceForm'>;

type MaintenanceRecord = {
  id?: string;
  date: string;
  type: string;
  description: string;
  cost: string;
  performedBy: string;
  notes: string;
};

const MaintenanceFormScreen: React.FC<MaintenanceFormScreenProps> = ({ route, navigation }) => {
  const { equipmentId, recordId } = route.params;
  const isEditing = !!recordId;
  
  const [record, setRecord] = useState<MaintenanceRecord>({
    date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
    type: 'Regular Maintenance',
    description: '',
    cost: '',
    performedBy: '',
    notes: '',
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (field: keyof MaintenanceRecord, value: string) => {
    setRecord(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!record.date.trim()) {
      newErrors.date = 'Date is required';
    } else if (!/^\d{4}-\d{2}-\d{2}$/.test(record.date)) {
      newErrors.date = 'Date format should be YYYY-MM-DD';
    }
    
    if (!record.type.trim()) {
      newErrors.type = 'Type is required';
    }
    
    if (!record.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    if (record.cost && isNaN(Number(record.cost))) {
      newErrors.cost = 'Cost must be a number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please correct the errors in the form.');
      return;
    }
    
    // This would normally be an API call to save the maintenance record
    // For now, we'll just show a success message and navigate back
    Alert.alert(
      isEditing ? 'Maintenance Record Updated' : 'Maintenance Record Added',
      `Maintenance record has been ${isEditing ? 'updated' : 'added'} successfully.`,
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const maintenanceTypes = [
    'Regular Maintenance',
    'Repair',
    'Inspection',
    'Oil Change',
    'Filter Replacement',
    'Tire Replacement',
    'Belt Replacement',
    'Other',
  ];

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading maintenance record...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>
            {isEditing ? 'Edit Maintenance Record' : 'Add Maintenance Record'}
          </Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Date *</Text>
            <TextInput
              style={[styles.input, errors.date && styles.inputError]}
              value={record.date}
              onChangeText={(value) => handleChange('date', value)}
              placeholder="YYYY-MM-DD"
            />
            {errors.date && <Text style={styles.errorText}>{errors.date}</Text>}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Type *</Text>
            <View style={[styles.pickerContainer, errors.type && styles.inputError]}>
              <Picker
                selectedValue={record.type}
                onValueChange={(value) => handleChange('type', value)}
                style={styles.picker}
              >
                {maintenanceTypes.map((type) => (
                  <Picker.Item key={type} label={type} value={type} />
                ))}
              </Picker>
            </View>
            {errors.type && <Text style={styles.errorText}>{errors.type}</Text>}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.input, errors.description && styles.inputError]}
              value={record.description}
              onChangeText={(value) => handleChange('description', value)}
              placeholder="Enter description of maintenance or repair"
            />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Cost</Text>
            <TextInput
              style={[styles.input, errors.cost && styles.inputError]}
              value={record.cost}
              onChangeText={(value) => handleChange('cost', value)}
              placeholder="Enter cost"
              keyboardType="numeric"
            />
            {errors.cost && <Text style={styles.errorText}>{errors.cost}</Text>}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Performed By</Text>
            <TextInput
              style={styles.input}
              value={record.performedBy}
              onChangeText={(value) => handleChange('performedBy', value)}
              placeholder="Enter name of person or company"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={record.notes}
              onChangeText={(value) => handleChange('notes', value)}
              placeholder="Enter additional notes"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Text style={styles.submitButtonText}>
                {isEditing ? 'Update Record' : 'Add Record'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 5,
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#22c55e',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default MaintenanceFormScreen;