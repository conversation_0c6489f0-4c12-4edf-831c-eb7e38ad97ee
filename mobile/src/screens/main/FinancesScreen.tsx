import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';

type FinancesScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Finances'>,
  NativeStackScreenProps<MainStackParamList>
>;

type FinancialSummary = {
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
  pendingInvoices: number;
  unpaidInvoicesAmount: number;
};

type RecentTransaction = {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  date: string;
  description: string;
  category?: string;
  status?: string;
};

const FinancesScreen: React.FC<FinancesScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  
  // Mock financial summary data
  const financialSummary: FinancialSummary = {
    totalIncome: 125000,
    totalExpenses: 78500,
    netProfit: 46500,
    pendingInvoices: 5,
    unpaidInvoicesAmount: 12500,
  };
  
  // Mock recent transactions
  const recentTransactions: RecentTransaction[] = [
    {
      id: '1',
      type: 'income',
      amount: 5000,
      date: '2023-09-15',
      description: 'Corn sale',
      category: 'Sales',
      status: 'Completed',
    },
    {
      id: '2',
      type: 'expense',
      amount: 1200,
      date: '2023-09-12',
      description: 'Fertilizer purchase',
      category: 'Supplies',
      status: 'Completed',
    },
    {
      id: '3',
      type: 'income',
      amount: 3500,
      date: '2023-09-10',
      description: 'Equipment rental',
      category: 'Services',
      status: 'Completed',
    },
    {
      id: '4',
      type: 'expense',
      amount: 850,
      date: '2023-09-08',
      description: 'Fuel',
      category: 'Fuel',
      status: 'Completed',
    },
    {
      id: '5',
      type: 'expense',
      amount: 2500,
      date: '2023-09-05',
      description: 'Equipment repair',
      category: 'Maintenance',
      status: 'Completed',
    },
  ];

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const renderTransactionItem = ({ item }: { item: RecentTransaction }) => (
    <TouchableOpacity 
      style={styles.transactionItem}
      onPress={() => {
        if (item.type === 'income') {
          // Navigate to invoice detail
          // navigation.navigate('InvoiceDetail', { invoiceId: item.id });
          alert('Navigate to invoice detail');
        } else {
          // Navigate to expense detail
          // navigation.navigate('ExpenseDetail', { expenseId: item.id });
          alert('Navigate to expense detail');
        }
      }}
    >
      <View style={styles.transactionIcon}>
        <Ionicons 
          name={item.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'} 
          size={24} 
          color={item.type === 'income' ? '#22c55e' : '#ef4444'} 
        />
      </View>
      <View style={styles.transactionInfo}>
        <Text style={styles.transactionDescription}>{item.description}</Text>
        <Text style={styles.transactionCategory}>{item.category}</Text>
        <Text style={styles.transactionDate}>{item.date}</Text>
      </View>
      <Text style={[
        styles.transactionAmount,
        { color: item.type === 'income' ? '#22c55e' : '#ef4444' }
      ]}>
        {item.type === 'income' ? '+' : '-'}{formatCurrency(item.amount)}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Financial Summary Card */}
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Financial Summary</Text>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Income</Text>
            <Text style={[styles.summaryValue, styles.incomeText]}>
              {formatCurrency(financialSummary.totalIncome)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Expenses</Text>
            <Text style={[styles.summaryValue, styles.expenseText]}>
              {formatCurrency(financialSummary.totalExpenses)}
            </Text>
          </View>
        </View>
        <View style={styles.divider} />
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Net Profit</Text>
            <Text style={[styles.summaryValue, styles.profitText]}>
              {formatCurrency(financialSummary.netProfit)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Pending Invoices</Text>
            <Text style={styles.summaryValue}>
              {financialSummary.pendingInvoices} ({formatCurrency(financialSummary.unpaidInvoicesAmount)})
            </Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('ExpensesScreen')}
        >
          <View style={[styles.actionIcon, { backgroundColor: '#ef4444' }]}>
            <Ionicons name="cash-outline" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>Expenses</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('InvoicesScreen')}
        >
          <View style={[styles.actionIcon, { backgroundColor: '#22c55e' }]}>
            <Ionicons name="document-text-outline" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>Invoices</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('CustomersScreen')}
        >
          <View style={[styles.actionIcon, { backgroundColor: '#3b82f6' }]}>
            <Ionicons name="people-outline" size={24} color="#fff" />
          </View>
          <Text style={styles.actionText}>Customers</Text>
        </TouchableOpacity>
      </View>

      {/* Recent Transactions */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={recentTransactions}
          renderItem={renderTransactionItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    margin: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  incomeText: {
    color: '#22c55e',
  },
  expenseText: {
    color: '#ef4444',
  },
  profitText: {
    color: '#22c55e',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 10,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#333',
  },
  sectionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: '#22c55e',
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  transactionCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FinancesScreen;