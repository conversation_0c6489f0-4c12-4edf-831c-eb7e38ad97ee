import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';

type EmployeesScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Employees'>,
  NativeStackScreenProps<MainStackParamList>
>;

type Employee = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  startDate?: string;
  hourlyRate?: number;
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE';
};

const EmployeesScreen: React.FC<EmployeesScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);

  useEffect(() => {
    // This would normally be an API call
    // For now, we'll use mock data
    const mockEmployees: Employee[] = [
      { 
        id: '1', 
        name: 'John Smith', 
        email: '<EMAIL>', 
        phone: '(*************', 
        position: 'Farm Manager', 
        startDate: '2020-03-15', 
        hourlyRate: 25.50,
        status: 'ACTIVE'
      },
      { 
        id: '2', 
        name: 'Sarah Johnson', 
        email: '<EMAIL>', 
        phone: '(*************', 
        position: 'Equipment Operator', 
        startDate: '2021-05-10', 
        hourlyRate: 18.75,
        status: 'ACTIVE'
      },
      { 
        id: '3', 
        name: 'Michael Brown', 
        email: '<EMAIL>', 
        phone: '(*************', 
        position: 'Field Worker', 
        startDate: '2022-02-20', 
        hourlyRate: 16.50,
        status: 'ACTIVE'
      },
      { 
        id: '4', 
        name: 'Emily Davis', 
        email: '<EMAIL>', 
        phone: '(*************', 
        position: 'Administrative Assistant', 
        startDate: '2021-08-05', 
        hourlyRate: 17.25,
        status: 'ON_LEAVE'
      },
      { 
        id: '5', 
        name: 'Robert Wilson', 
        email: '<EMAIL>', 
        phone: '(*************', 
        position: 'Seasonal Worker', 
        startDate: '2023-04-10', 
        hourlyRate: 15.00,
        status: 'INACTIVE'
      },
    ];

    setEmployees(mockEmployees);
    setFilteredEmployees(mockEmployees);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (searchQuery) {
      const filtered = employees.filter(employee => 
        employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (employee.email && employee.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (employee.position && employee.position.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (employee.phone && employee.phone.includes(searchQuery))
      );
      setFilteredEmployees(filtered);
    } else {
      setFilteredEmployees(employees);
    }
  }, [searchQuery, employees]);

  const handleAddEmployee = () => {
    // In a real app, we would navigate to an employee creation screen
    Alert.alert(
      'Add New Employee',
      'This functionality would allow you to add new employees to your farm.'
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '#22c55e';
      case 'INACTIVE':
        return '#9ca3af';
      case 'ON_LEAVE':
        return '#f59e0b';
      default:
        return '#9ca3af';
    }
  };

  const formatCurrency = (amount?: number) => {
    if (amount === undefined) return 'N/A';
    return `$${amount.toFixed(2)}/hr`;
  };

  const renderEmployeeItem = ({ item }: { item: Employee }) => (
    <TouchableOpacity
      style={styles.employeeItem}
      onPress={() => navigation.navigate('EmployeeDetail', { employeeId: item.id })}
    >
      <View style={styles.employeeIcon}>
        <Ionicons 
          name="person-outline" 
          size={24} 
          color="#22c55e" 
        />
      </View>
      <View style={styles.employeeInfo}>
        <Text style={styles.employeeName}>{item.name}</Text>
        <Text style={styles.employeePosition}>{item.position || 'No position'}</Text>
        <View style={styles.employeeDetails}>
          <View style={styles.employeeStatus}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor(item.status) }
            ]} />
            <Text style={styles.statusText}>{item.status.replace('_', ' ')}</Text>
          </View>
          {item.hourlyRate && (
            <Text style={styles.employeeRate}>{formatCurrency(item.hourlyRate)}</Text>
          )}
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading employees...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search employees..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredEmployees.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="people" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No employees found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? "Try a different search term" 
              : "Add your first employee to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredEmployees}
          renderItem={renderEmployeeItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity style={styles.addButton} onPress={handleAddEmployee}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  employeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  employeeIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
  },
  employeePosition: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  employeeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  employeeStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  employeeRate: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default EmployeesScreen;