import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { useNotificationStore } from '@/store/notificationStore';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainTabParamList } from '@/navigation/MainNavigator';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainStackParamList } from '@/navigation/MainNavigator';

type ProfileScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Profile'>,
  NativeStackScreenProps<MainStackParamList>
>;

const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user, logout } = useAuth();
  const {
    pushToken,
    isNotificationsEnabled,
    notificationSettings,
    setNotificationsEnabled,
    updateNotificationSettings,
    initializePushNotifications,
  } = useNotificationStore();

  useEffect(() => {
    // Initialize push notifications when the profile screen is loaded
    if (!pushToken) {
      initializePushNotifications();
    }
  }, [pushToken, initializePushNotifications]);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Logout Failed', 'There was an error logging out. Please try again.');
    }
  };

  const toggleNotifications = (value: boolean) => {
    setNotificationsEnabled(value);
    if (value && !pushToken) {
      initializePushNotifications();
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.profileIconContainer}>
          <Ionicons name="person" size={60} color="#fff" />
        </View>
        <Text style={styles.userName}>{user?.name}</Text>
        <Text style={styles.userEmail}>{user?.email}</Text>
        <Text style={styles.farmName}>
          {user?.farms.find(f => f.id === user.currentFarmId)?.name}
        </Text>
      </View>

      {/* Notification Settings */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Notification Settings</Text>
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Enable Notifications</Text>
            <Text style={styles.settingDescription}>
              Receive push notifications on your device
            </Text>
          </View>
          <Switch
            value={isNotificationsEnabled}
            onValueChange={toggleNotifications}
            trackColor={{ false: '#d1d5db', true: '#86efac' }}
            thumbColor={isNotificationsEnabled ? '#22c55e' : '#f4f4f5'}
          />
        </View>

        {isNotificationsEnabled && (
          <>
            <View style={styles.settingItem}>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>Task Assignments</Text>
                <Text style={styles.settingDescription}>
                  Get notified when tasks are assigned to you
                </Text>
              </View>
              <Switch
                value={notificationSettings.taskAssignments}
                onValueChange={(value) => updateNotificationSettings({ taskAssignments: value })}
                trackColor={{ false: '#d1d5db', true: '#86efac' }}
                thumbColor={notificationSettings.taskAssignments ? '#22c55e' : '#f4f4f5'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>Due Date Alerts</Text>
                <Text style={styles.settingDescription}>
                  Get reminders for upcoming and overdue tasks
                </Text>
              </View>
              <Switch
                value={notificationSettings.dueDateAlerts}
                onValueChange={(value) => updateNotificationSettings({ dueDateAlerts: value })}
                trackColor={{ false: '#d1d5db', true: '#86efac' }}
                thumbColor={notificationSettings.dueDateAlerts ? '#22c55e' : '#f4f4f5'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>Status Changes</Text>
                <Text style={styles.settingDescription}>
                  Get notified when task status changes
                </Text>
              </View>
              <Switch
                value={notificationSettings.statusChanges}
                onValueChange={(value) => updateNotificationSettings({ statusChanges: value })}
                trackColor={{ false: '#d1d5db', true: '#86efac' }}
                thumbColor={notificationSettings.statusChanges ? '#22c55e' : '#f4f4f5'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>System Announcements</Text>
                <Text style={styles.settingDescription}>
                  Receive important system updates and announcements
                </Text>
              </View>
              <Switch
                value={notificationSettings.systemAnnouncements}
                onValueChange={(value) => updateNotificationSettings({ systemAnnouncements: value })}
                trackColor={{ false: '#d1d5db', true: '#86efac' }}
                thumbColor={notificationSettings.systemAnnouncements ? '#22c55e' : '#f4f4f5'}
              />
            </View>
          </>
        )}
      </View>

      {/* Account Settings */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
        </View>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="person-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>Edit Profile</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="lock-closed-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>Change Password</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="business-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>Manage Farms</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      {/* Support */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Support</Text>
        </View>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="help-circle-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>Help & Support</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="document-text-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>Terms & Privacy Policy</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuIconContainer}>
            <Ionicons name="information-circle-outline" size={24} color="#22c55e" />
          </View>
          <Text style={styles.menuText}>About</Text>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Ionicons name="log-out-outline" size={20} color="#fff" style={styles.logoutIcon} />
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>

      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  profileHeader: {
    backgroundColor: '#22c55e',
    padding: 20,
    alignItems: 'center',
    paddingBottom: 30,
  },
  profileIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  farmName: {
    fontSize: 16,
    color: '#fff',
    marginTop: 5,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginTop: 20,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    borderRadius: 8,
    marginHorizontal: 15,
    marginTop: 20,
    marginBottom: 10,
    padding: 15,
  },
  logoutIcon: {
    marginRight: 8,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  versionContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#999',
  },
});

export default ProfileScreen;