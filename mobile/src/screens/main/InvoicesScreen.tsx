import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/store/AuthProvider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MainTabParamList, MainStackParamList } from '@/navigation/MainNavigator';
import invoiceService, { Invoice } from '@/services/invoiceService';

type InvoicesScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Finances'>,
  NativeStackScreenProps<MainStackParamList>
>;

const InvoicesScreen: React.FC<InvoicesScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);

  useEffect(() => {
    const fetchInvoices = async () => {
      setLoading(true);
      try {
        if (user && user.currentFarmId) {
          const invoicesData = await invoiceService.getInvoices(user.currentFarmId);
          setInvoices(invoicesData);
          setFilteredInvoices(invoicesData);
        } else {
          // If no farm ID is available, show an error
          Alert.alert('Error', 'No farm selected');
        }
      } catch (error) {
        console.error('Error fetching invoices:', error);
        Alert.alert('Error', 'An error occurred while loading invoices');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [user]);

  useEffect(() => {
    if (searchQuery) {
      const filtered = invoices.filter(invoice => 
        invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (invoice.notes && invoice.notes.toLowerCase().includes(searchQuery.toLowerCase())) ||
        invoice.issueDate.includes(searchQuery) ||
        invoice.dueDate.includes(searchQuery)
      );
      setFilteredInvoices(filtered);
    } else {
      setFilteredInvoices(invoices);
    }
  }, [searchQuery, invoices]);

  const handleAddInvoice = () => {
    // In a real app, we would navigate to an invoice creation screen
    Alert.alert(
      'Add New Invoice',
      'This functionality would allow you to create a new invoice.'
    );
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '#9ca3af';
      case 'SENT':
        return '#3b82f6';
      case 'PAID':
        return '#22c55e';
      case 'OVERDUE':
        return '#ef4444';
      case 'CANCELLED':
        return '#6b7280';
      default:
        return '#9ca3af';
    }
  };

  const renderInvoiceItem = ({ item }: { item: Invoice }) => (
    <TouchableOpacity
      style={styles.invoiceItem}
      onPress={() => navigation.navigate('InvoiceDetail', { invoiceId: item.id })}
    >
      <View style={styles.invoiceHeader}>
        <Text style={styles.invoiceNumber}>{item.invoiceNumber}</Text>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) }
        ]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <View style={styles.invoiceInfo}>
        <Text style={styles.customerName}>{item.customerName}</Text>
        <View style={styles.invoiceDates}>
          <Text style={styles.dateLabel}>Issued: </Text>
          <Text style={styles.dateValue}>{item.issueDate}</Text>
          <Text style={styles.dateLabel}> • Due: </Text>
          <Text style={styles.dateValue}>{item.dueDate}</Text>
        </View>
        {item.notes && (
          <Text style={styles.invoiceNotes} numberOfLines={1} ellipsizeMode="tail">
            {item.notes}
          </Text>
        )}
      </View>
      <View style={styles.invoiceAmount}>
        <Text style={styles.amountLabel}>Total</Text>
        <Text style={styles.amountValue}>{formatCurrency(item.total)}</Text>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading invoices...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search invoices..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        ) : null}
      </View>

      {filteredInvoices.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No invoices found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery 
              ? "Try a different search term" 
              : "Add your first invoice to get started"}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredInvoices}
          renderItem={renderInvoiceItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity style={styles.addButton} onPress={handleAddInvoice}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 80, // Space for the add button
  },
  invoiceItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  invoiceNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  invoiceInfo: {
    marginBottom: 10,
  },
  customerName: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  invoiceDates: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  dateLabel: {
    fontSize: 12,
    color: '#666',
  },
  dateValue: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  invoiceNotes: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  invoiceAmount: {
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 10,
    alignItems: 'flex-end',
  },
  amountLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  amountValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#22c55e',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default InvoicesScreen;
