import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';

type TimeEntryScreenProps = NativeStackScreenProps<MainStackParamList, 'TimeEntry'>;

type Employee = {
  id: string;
  name: string;
  position?: string;
};

type Task = {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  assignedTo?: string;
  fieldId?: string;
  fieldName?: string;
  equipmentId?: string;
  equipmentName?: string;
};

type TimeEntry = {
  id?: string;
  employeeId?: string;
  taskId?: string;
  date: string;
  startTime: string;
  endTime?: string;
  hours?: number;
  description?: string;
  isCompleted: boolean;
};

const TimeEntryScreen: React.FC<TimeEntryScreenProps> = ({ route, navigation }) => {
  const { employeeId, taskId, timeEntryId } = route.params;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [task, setTask] = useState<Task | null>(null);
  const [timeEntry, setTimeEntry] = useState<TimeEntry>({
    employeeId,
    taskId,
    date: new Date().toISOString().split('T')[0],
    startTime: '08:00',
    endTime: '17:00',
    hours: 8,
    description: '',
    isCompleted: true,
  });

  useEffect(() => {
    // This would normally be an API call to get employee details, task details,
    // and time entry details if editing an existing entry
    const fetchData = async () => {
      try {
        // Mock employee data if employeeId is provided
        if (employeeId) {
          const mockEmployee: Employee = {
            id: employeeId,
            name: employeeId === '1' ? 'John Smith' : 
                  employeeId === '2' ? 'Sarah Johnson' : 
                  employeeId === '3' ? 'Michael Brown' : 
                  employeeId === '4' ? 'Emily Davis' : 'Robert Wilson',
            position: employeeId === '1' ? 'Farm Manager' : 
                      employeeId === '2' ? 'Equipment Operator' : 
                      employeeId === '3' ? 'Field Worker' : 
                      employeeId === '4' ? 'Administrative Assistant' : 'Seasonal Worker',
          };

          setEmployee(mockEmployee);
        }

        // Mock task data if taskId is provided
        if (taskId) {
          const mockTask: Task = {
            id: taskId,
            title: taskId === '1' ? 'Spray north field' : 
                   taskId === '2' ? 'Repair tractor' : 
                   taskId === '3' ? 'Order seeds' : 
                   'Field maintenance',
            description: taskId === '1' ? 'Apply herbicide to control weeds in the north field' : 
                         taskId === '2' ? 'Fix hydraulic system on the John Deere tractor' : 
                         taskId === '3' ? 'Order corn seeds for next season' : 
                         'General field maintenance tasks',
            status: taskId === '1' ? 'pending' : 
                    taskId === '2' ? 'in-progress' : 
                    taskId === '3' ? 'pending' : 
                    'pending',
            priority: taskId === '1' ? 'high' : 
                      taskId === '2' ? 'urgent' : 
                      taskId === '3' ? 'medium' : 
                      'low',
            dueDate: taskId === '1' ? '2023-09-15' : 
                     taskId === '2' ? '2023-09-16' : 
                     taskId === '3' ? '2023-09-18' : 
                     '2023-09-20',
            assignedTo: taskId === '1' ? 'John Doe' : 
                        taskId === '2' ? 'Jane Smith' : 
                        taskId === '3' ? 'John Doe' : 
                        undefined,
            fieldId: taskId === '1' ? '1' : undefined,
            fieldName: taskId === '1' ? 'North Field' : undefined,
            equipmentId: taskId === '2' ? '1' : undefined,
            equipmentName: taskId === '2' ? 'John Deere 8R' : undefined,
          };

          setTask(mockTask);

          // If no description is set yet, use the task title as default
          if (!timeEntry.description) {
            setTimeEntry(prev => ({ 
              ...prev, 
              description: `Work on task: ${mockTask.title}` 
            }));
          }
        }

        // If editing an existing time entry, fetch its details
        if (timeEntryId) {
          // Mock time entry data
          const mockTimeEntry: TimeEntry = {
            id: timeEntryId,
            employeeId,
            taskId,
            date: '2023-09-10',
            startTime: '08:00',
            endTime: '17:00',
            hours: 8,
            description: taskId ? `Work on task: ${task?.title || 'Unknown task'}` : 'Field work - North Field',
            isCompleted: true,
          };

          setTimeEntry(mockTimeEntry);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert('Error', 'Failed to load data');
        navigation.goBack();
      }
    };

    fetchData();
  }, [employeeId, taskId, timeEntryId, navigation, task?.title]);

  const handleSave = async () => {
    // Validate form
    if (!timeEntry.date) {
      Alert.alert('Error', 'Please enter a date');
      return;
    }

    if (!timeEntry.startTime) {
      Alert.alert('Error', 'Please enter a start time');
      return;
    }

    if (timeEntry.isCompleted && !timeEntry.endTime) {
      Alert.alert('Error', 'Please enter an end time for completed entries');
      return;
    }

    if (timeEntry.isCompleted && !timeEntry.hours) {
      Alert.alert('Error', 'Please enter the number of hours worked');
      return;
    }

    setSaving(true);

    try {
      // This would normally be an API call to save the time entry
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back to the employee detail screen
      navigation.goBack();

      // Show success message
      Alert.alert(
        'Success',
        timeEntryId ? 'Time entry updated successfully' : 'Time entry added successfully'
      );
    } catch (error) {
      console.error('Error saving time entry:', error);
      Alert.alert('Error', 'Failed to save time entry');
      setSaving(false);
    }
  };

  const calculateHours = () => {
    if (!timeEntry.startTime || !timeEntry.endTime) return;

    const [startHour, startMinute] = timeEntry.startTime.split(':').map(Number);
    const [endHour, endMinute] = timeEntry.endTime.split(':').map(Number);

    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;

    // Calculate difference in hours, rounded to nearest quarter hour
    const diffInMinutes = endTimeInMinutes - startTimeInMinutes;
    const hours = Math.round(diffInMinutes / 15) / 4;

    setTimeEntry(prev => ({ ...prev, hours }));
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {timeEntryId ? 'Edit Time Entry' : 'New Time Entry'}
        </Text>

        {task && (
          <View style={styles.taskContainer}>
            <Text style={styles.taskTitle}>{task.title}</Text>
            <View style={styles.taskDetails}>
              <Text style={styles.taskStatus}>Status: {task.status}</Text>
              <Text style={styles.taskPriority}>Priority: {task.priority}</Text>
            </View>
            {task.assignedTo && (
              <Text style={styles.taskAssignee}>Assigned to: {task.assignedTo}</Text>
            )}
          </View>
        )}

        {employee && (
          <View style={styles.employeeContainer}>
            <Text style={styles.employeeName}>{employee.name}</Text>
            {employee.position && (
              <Text style={styles.employeePosition}>{employee.position}</Text>
            )}
          </View>
        )}
      </View>

      <View style={styles.formContainer}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>Date *</Text>
          <TextInput
            style={styles.input}
            value={timeEntry.date}
            onChangeText={(text) => setTimeEntry(prev => ({ ...prev, date: text }))}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 10 }]}>
            <Text style={styles.label}>Start Time *</Text>
            <TextInput
              style={styles.input}
              value={timeEntry.startTime}
              onChangeText={(text) => {
                setTimeEntry(prev => ({ ...prev, startTime: text }));
                if (timeEntry.endTime) calculateHours();
              }}
              placeholder="HH:MM"
            />
          </View>

          <View style={[styles.formGroup, { flex: 1 }]}>
            <Text style={styles.label}>End Time {timeEntry.isCompleted ? '*' : ''}</Text>
            <TextInput
              style={styles.input}
              value={timeEntry.endTime}
              onChangeText={(text) => {
                setTimeEntry(prev => ({ ...prev, endTime: text }));
                if (text) calculateHours();
              }}
              placeholder="HH:MM"
              editable={timeEntry.isCompleted}
            />
          </View>
        </View>

        <View style={styles.formGroup}>
          <View style={styles.completedRow}>
            <Text style={styles.label}>Completed</Text>
            <Switch
              value={timeEntry.isCompleted}
              onValueChange={(value) => setTimeEntry(prev => ({ ...prev, isCompleted: value }))}
              trackColor={{ false: '#d1d5db', true: '#86efac' }}
              thumbColor={timeEntry.isCompleted ? '#22c55e' : '#f4f4f5'}
            />
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Hours {timeEntry.isCompleted ? '*' : ''}</Text>
          <TextInput
            style={styles.input}
            value={timeEntry.hours?.toString() || ''}
            onChangeText={(text) => setTimeEntry(prev => ({ ...prev, hours: text ? parseFloat(text) : undefined }))}
            keyboardType="numeric"
            placeholder="0.00"
            editable={timeEntry.isCompleted}
          />
          <Text style={styles.helperText}>
            Hours are automatically calculated from start and end times, but can be manually adjusted
          </Text>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={timeEntry.description}
            onChangeText={(text) => setTimeEntry(prev => ({ ...prev, description: text }))}
            placeholder="Enter a description of the work performed"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
            disabled={saving}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  taskContainer: {
    backgroundColor: '#f0fdf4',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    borderLeftWidth: 3,
    borderLeftColor: '#22c55e',
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  taskDetails: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  taskStatus: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  taskPriority: {
    fontSize: 14,
    color: '#666',
  },
  taskAssignee: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  employeeContainer: {
    marginTop: 5,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  employeePosition: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  formContainer: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
  },
  completedRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#22c55e',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TimeEntryScreen;
