import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';

type ExpenseDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'ExpenseDetail'>;

type Expense = {
  id: string;
  category: string;
  amount: number;
  date: string;
  description?: string;
  vendor?: string;
  receiptUrl?: string;
  notes?: string;
  paymentMethod?: string;
  paymentStatus?: 'PAID' | 'PENDING' | 'CANCELLED';
  tags?: string[];
};

const ExpenseDetailScreen: React.FC<ExpenseDetailScreenProps> = ({ route, navigation }) => {
  const { expenseId } = route.params;
  const [expense, setExpense] = useState<Expense | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // This would normally be an API call
    // For now, we'll use mock data
    const mockExpense: Expense = {
      id: expenseId,
      category: expenseId === '1' ? 'Supplies' : 
               expenseId === '2' ? 'Fuel' : 
               expenseId === '3' ? 'Maintenance' : 
               expenseId === '4' ? 'Seeds' : 'Labor',
      amount: expenseId === '1' ? 1200 : 
              expenseId === '2' ? 850 : 
              expenseId === '3' ? 2500 : 
              expenseId === '4' ? 3500 : 4200,
      date: expenseId === '1' ? '2023-09-12' : 
            expenseId === '2' ? '2023-09-08' : 
            expenseId === '3' ? '2023-09-05' : 
            expenseId === '4' ? '2023-08-28' : '2023-08-25',
      description: expenseId === '1' ? 'Fertilizer purchase' : 
                   expenseId === '2' ? 'Diesel for tractors' : 
                   expenseId === '3' ? 'Equipment repair - John Deere tractor' : 
                   expenseId === '4' ? 'Corn seeds for next season' : 'Seasonal workers - August',
      vendor: expenseId === '1' ? 'AgriSupply Co.' : 
              expenseId === '2' ? 'Shell Gas Station' : 
              expenseId === '3' ? 'John Deere Service Center' : 
              expenseId === '4' ? 'Pioneer Seeds' : 'Farm Labor LLC',
      receiptUrl: expenseId === '1' || expenseId === '3' || expenseId === '4' 
                  ? `https://example.com/receipts/${expenseId}.pdf` 
                  : undefined,
      notes: expenseId === '1' ? 'Purchased fertilizer for the north and east fields.' : 
             expenseId === '3' ? 'Hydraulic system repair on the John Deere tractor.' : 
             expenseId === '4' ? 'Pre-purchased seeds for next season at a discount.' : undefined,
      paymentMethod: expenseId === '1' ? 'Credit Card' : 
                     expenseId === '2' ? 'Debit Card' : 
                     expenseId === '3' ? 'Check' : 
                     expenseId === '4' ? 'Bank Transfer' : 'Cash',
      paymentStatus: expenseId === '1' || expenseId === '2' ? 'PAID' : 
                     expenseId === '3' ? 'PENDING' : 'PAID',
      tags: expenseId === '1' ? ['Fertilizer', 'North Field', 'East Field'] : 
            expenseId === '3' ? ['Repair', 'Tractor', 'Maintenance'] : 
            expenseId === '4' ? ['Seeds', 'Corn', 'Next Season'] : undefined,
    };

    setExpense(mockExpense);
    setLoading(false);
  }, [expenseId]);

  const handleEditExpense = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Expense', 'Expense editing functionality would be implemented here.');
  };

  const handleViewReceipt = () => {
    // In a real app, this would open the receipt image/PDF
    Alert.alert('View Receipt', 'Receipt viewing functionality would be implemented here.');
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  const getPaymentStatusColor = (status?: string) => {
    switch (status) {
      case 'PAID':
        return '#22c55e';
      case 'PENDING':
        return '#f59e0b';
      case 'CANCELLED':
        return '#ef4444';
      default:
        return '#9ca3af';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'supplies':
        return 'cart-outline';
      case 'fuel':
        return 'flame-outline';
      case 'maintenance':
        return 'construct-outline';
      case 'seeds':
        return 'leaf-outline';
      case 'labor':
        return 'people-outline';
      case 'utilities':
        return 'flash-outline';
      case 'rent':
        return 'home-outline';
      case 'insurance':
        return 'shield-outline';
      case 'taxes':
        return 'document-text-outline';
      default:
        return 'cash-outline';
    }
  };

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Expense Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Category:</Text>
          <Text style={styles.infoValue}>{expense?.category}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Amount:</Text>
          <Text style={[styles.infoValue, styles.amountText]}>
            {formatCurrency(expense?.amount || 0)}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Date:</Text>
          <Text style={styles.infoValue}>{formatDate(expense?.date || '')}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Vendor:</Text>
          <Text style={styles.infoValue}>{expense?.vendor || 'N/A'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Payment Method:</Text>
          <Text style={styles.infoValue}>{expense?.paymentMethod || 'N/A'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Payment Status:</Text>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getPaymentStatusColor(expense?.paymentStatus) }
            ]} />
            <Text style={styles.statusText}>{expense?.paymentStatus || 'N/A'}</Text>
          </View>
        </View>
      </View>

      {expense?.description && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Description</Text>
          <Text style={styles.descriptionText}>{expense.description}</Text>
        </View>
      )}

      {expense?.notes && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Notes</Text>
          <Text style={styles.notesText}>{expense.notes}</Text>
        </View>
      )}

      {expense?.tags && expense.tags.length > 0 && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Tags</Text>
          <View style={styles.tagsContainer}>
            {expense.tags.map((tag, index) => (
              <View key={index} style={styles.tagBadge}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {expense?.receiptUrl && (
        <TouchableOpacity style={styles.receiptCard} onPress={handleViewReceipt}>
          <View style={styles.receiptHeader}>
            <Text style={styles.cardTitle}>Receipt</Text>
            <Ionicons name="document-outline" size={24} color="#666" />
          </View>
          <Text style={styles.receiptText}>
            Tap to view receipt
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading expense details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.expenseTitle}>{expense?.description || `${expense?.category} Expense`}</Text>
          <Text style={styles.expenseSubtitle}>
            {formatDate(expense?.date || '')} • {formatCurrency(expense?.amount || 0)}
          </Text>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={handleEditExpense}>
          <Ionicons name="create-outline" size={24} color="#ef4444" />
        </TouchableOpacity>
      </View>

      <View style={styles.categoryBadge}>
        <Ionicons name={getCategoryIcon(expense?.category || '')} size={16} color="#fff" />
        <Text style={styles.categoryText}>{expense?.category}</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        {renderOverviewTab()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flex: 1,
  },
  expenseTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  expenseSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ef4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginHorizontal: 20,
    marginTop: 10,
  },
  categoryText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
    marginLeft: 6,
  },
  scrollView: {
    flex: 1,
    marginTop: 10,
  },
  tabContent: {
    padding: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  amountText: {
    color: '#ef4444',
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  descriptionText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagBadge: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#666',
  },
  receiptCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  receiptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  receiptText: {
    fontSize: 14,
    color: '#3b82f6',
    textAlign: 'center',
    paddingVertical: 20,
  },
});

export default ExpenseDetailScreen;