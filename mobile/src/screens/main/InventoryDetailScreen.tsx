import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { inventoryService, InventoryItem, InventoryTransaction } from '@/services/inventoryService';

type InventoryDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'InventoryDetail'>;

const InventoryDetailScreen: React.FC<InventoryDetailScreenProps> = ({ route, navigation }) => {
  const { inventoryId } = route.params;
  const [inventory, setInventory] = useState<InventoryItem | null>(null);
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchInventoryData = async () => {
      try {
        setLoading(true);

        // Fetch inventory details with transactions
        const inventoryDetails = await inventoryService.getInventoryById(inventoryId);

        if (inventoryDetails) {
          setInventory(inventoryDetails);
          setTransactions(inventoryDetails.transactions || []);
        } else {
          // If inventory details couldn't be fetched, try to get individual data
          console.warn(`Could not fetch complete inventory details for inventory ${inventoryId}, trying individual endpoints`);

          // Try to get inventory data
          const inventoryData = await inventoryService.getInventoryById(inventoryId);
          if (inventoryData) {
            setInventory(inventoryData);
          } else {
            console.error(`Could not fetch inventory data for inventory ${inventoryId}`);
            Alert.alert('Error', 'Could not load inventory data. Please try again later.');
          }

          // Try to get transaction data
          const transactionData = await inventoryService.getInventoryTransactions(inventoryId);
          setTransactions(transactionData);
        }
      } catch (error) {
        console.error('Error fetching inventory data:', error);
        Alert.alert('Error', 'Could not load inventory data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (inventoryId) {
      fetchInventoryData();
    }
  }, [inventoryId]);

  const handleEditInventory = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Inventory', 'Inventory editing functionality would be implemented here.');
  };

  const handleAddTransaction = () => {
    // In a real app, this would navigate to a transaction creation screen
    Alert.alert('Add Transaction', 'Transaction creation functionality would be implemented here.');
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'seeds':
        return 'leaf-outline';
      case 'fertilizers':
        return 'flask-outline';
      case 'chemicals':
        return 'warning-outline';
      case 'fuel':
        return 'flame-outline';
      default:
        return 'cube-outline';
    }
  };

  const formatCurrency = (amount?: number) => {
    if (amount === undefined) return 'N/A';
    return `$${amount.toLocaleString()}`;
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'PURCHASE':
        return '#22c55e';
      case 'USE':
        return '#ef4444';
      case 'ADJUSTMENT':
        return '#f59e0b';
      case 'TRANSFER':
        return '#60a5fa';
      default:
        return '#9ca3af';
    }
  };

  const formatQuantity = (type: string, quantity: number, unit: string) => {
    const prefix = type === 'PURCHASE' ? '+' : type === 'USE' ? '-' : '';
    return `${prefix}${Math.abs(quantity)} ${unit}`;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading inventory data...</Text>
      </View>
    );
  }

  if (!inventory) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text style={styles.errorText}>Could not load inventory data.</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Inventory Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Category:</Text>
          <Text style={styles.infoValue}>{inventory?.category}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Quantity:</Text>
          <Text style={styles.infoValue}>{inventory?.quantity} {inventory?.unit}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Unit Price:</Text>
          <Text style={styles.infoValue}>
            {inventory?.unitPrice ? `${formatCurrency(inventory.unitPrice)}/${inventory.unit}` : 'N/A'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Total Value:</Text>
          <Text style={styles.infoValue}>
            {inventory?.unitPrice ? formatCurrency(inventory.unitPrice * inventory.quantity) : 'N/A'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Reorder Point:</Text>
          <Text style={styles.infoValue}>{inventory?.reorderPoint || 'N/A'}</Text>
        </View>
        {inventory?.reorderPoint && inventory.quantity <= inventory.reorderPoint && (
          <View style={styles.lowStockWarning}>
            <Ionicons name="warning-outline" size={16} color="#ef4444" />
            <Text style={styles.lowStockText}>Low stock! Consider reordering soon.</Text>
          </View>
        )}
      </View>

      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Supplier Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Supplier:</Text>
          <Text style={styles.infoValue}>{inventory?.supplier || 'N/A'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Location:</Text>
          <Text style={styles.infoValue}>{inventory?.location || 'N/A'}</Text>
        </View>
      </View>

      {inventory?.notes && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Notes</Text>
          <Text style={styles.notesText}>{inventory.notes}</Text>
        </View>
      )}
    </View>
  );

  const renderTransactionsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Transaction History</Text>
          <TouchableOpacity onPress={handleAddTransaction}>
            <Text style={styles.cardAction}>Add Transaction</Text>
          </TouchableOpacity>
        </View>
        {transactions.length > 0 ? (
          transactions.map(transaction => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionHeader}>
                <Text style={styles.transactionDate}>{transaction.date}</Text>
                <Text style={[
                  styles.transactionType,
                  { color: getTransactionTypeColor(transaction.type) }
                ]}>
                  {transaction.type}
                </Text>
              </View>
              <View style={styles.transactionDetails}>
                <Text style={[
                  styles.transactionQuantity,
                  { color: transaction.type === 'PURCHASE' ? '#22c55e' : 
                           transaction.type === 'USE' ? '#ef4444' : '#666' }
                ]}>
                  {formatQuantity(transaction.type, transaction.quantity, inventory?.unit || '')}
                </Text>
                {transaction.notes && (
                  <Text style={styles.transactionNotes}>{transaction.notes}</Text>
                )}
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No transactions for this inventory item</Text>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading inventory details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.inventoryName}>{inventory?.name}</Text>
          <Text style={styles.inventoryCategory}>{inventory?.category}</Text>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={handleEditInventory}>
          <Ionicons name="create-outline" size={24} color="#22c55e" />
        </TouchableOpacity>
      </View>

      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'overview' && styles.activeTabButton]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'overview' && styles.activeTabButtonText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'transactions' && styles.activeTabButton]}
          onPress={() => setActiveTab('transactions')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'transactions' && styles.activeTabButtonText]}>
            Transactions
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'transactions' && renderTransactionsTab()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: '#ef4444',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flex: 1,
  },
  inventoryName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  inventoryCategory: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#22c55e',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabButtonText: {
    color: '#22c55e',
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    padding: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  cardAction: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  lowStockWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fee2e2',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  lowStockText: {
    fontSize: 14,
    color: '#ef4444',
    marginLeft: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  transactionItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  transactionDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  transactionType: {
    fontSize: 14,
    fontWeight: '500',
  },
  transactionDetails: {
    marginTop: 5,
  },
  transactionQuantity: {
    fontSize: 14,
    fontWeight: '500',
  },
  transactionNotes: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    fontStyle: 'italic',
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 10,
  },
});

export default InventoryDetailScreen;
