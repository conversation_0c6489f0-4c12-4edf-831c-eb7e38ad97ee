import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { Picker } from '@react-native-picker/picker';
import { equipmentService, Equipment } from '@/services/equipmentService';
import { useAuth } from '@/store/AuthProvider';

type EquipmentFormScreenProps = NativeStackScreenProps<MainStackParamList, 'EquipmentForm'>;

const EquipmentFormScreen: React.FC<EquipmentFormScreenProps> = ({ route, navigation }) => {
  const equipmentId = route.params?.equipmentId;
  const isEditing = !!equipmentId;

  const [equipment, setEquipment] = useState<Equipment>({
    name: '',
    type: 'Tractor',
    make: '',
    model: '',
    year: '',
    serialNumber: '',
    purchaseDate: '',
    purchasePrice: '',
    currentValue: '',
    status: 'ACTIVE',
    notes: '',
  });

  const [loading, setLoading] = useState(isEditing);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { user } = useAuth();

  useEffect(() => {
    const fetchEquipmentData = async () => {
      if (isEditing && equipmentId) {
        try {
          setLoading(true);
          const equipmentData = await equipmentService.getEquipmentById(equipmentId);

          if (equipmentData) {
            // Convert numeric values to strings for form inputs
            const formattedEquipment = {
              ...equipmentData,
              year: equipmentData.year?.toString() || '',
              purchasePrice: equipmentData.purchasePrice?.toString() || '',
              currentValue: equipmentData.currentValue?.toString() || '',
            };
            setEquipment(formattedEquipment);
          } else {
            Alert.alert('Error', 'Could not load equipment data. Please try again later.');
            navigation.goBack();
          }
        } catch (error) {
          console.error('Error fetching equipment data:', error);
          Alert.alert('Error', 'Could not load equipment data. Please try again later.');
          navigation.goBack();
        } finally {
          setLoading(false);
        }
      }
    };

    fetchEquipmentData();
  }, [equipmentId, isEditing, navigation]);

  const handleChange = (field: keyof Equipment, value: string) => {
    setEquipment(prev => ({ ...prev, [field]: value }));

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!equipment.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!equipment.type.trim()) {
      newErrors.type = 'Type is required';
    }

    if (equipment.year && !/^\d{4}$/.test(equipment.year)) {
      newErrors.year = 'Year must be a 4-digit number';
    }

    if (equipment.purchaseDate && !/^\d{4}-\d{2}-\d{2}$/.test(equipment.purchaseDate)) {
      newErrors.purchaseDate = 'Date format should be YYYY-MM-DD';
    }

    if (equipment.purchasePrice && isNaN(Number(equipment.purchasePrice))) {
      newErrors.purchasePrice = 'Purchase price must be a number';
    }

    if (equipment.currentValue && isNaN(Number(equipment.currentValue))) {
      newErrors.currentValue = 'Current value must be a number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please correct the errors in the form.');
      return;
    }

    if (!user?.farmId) {
      Alert.alert('Error', 'No farm ID available. Please log in again.');
      return;
    }

    setLoading(true);

    try {
      // Prepare equipment data for API
      const equipmentData = {
        ...equipment,
        // Convert string values to numbers where needed
        year: equipment.year ? parseInt(equipment.year, 10) : undefined,
        purchasePrice: equipment.purchasePrice ? parseFloat(equipment.purchasePrice) : undefined,
        currentValue: equipment.currentValue ? parseFloat(equipment.currentValue) : undefined,
        farmId: user.farmId,
      };

      let result;
      if (isEditing && equipment.id) {
        // Update existing equipment
        result = await equipmentService.updateEquipment(equipment.id, equipmentData);
      } else {
        // Create new equipment
        result = await equipmentService.createEquipment(equipmentData);
      }

      if (result) {
        Alert.alert(
          isEditing ? 'Equipment Updated' : 'Equipment Added',
          `${equipment.name} has been ${isEditing ? 'updated' : 'added'} successfully.`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} equipment. Please try again.`);
      }
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} equipment:`, error);
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} equipment. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const equipmentTypes = [
    'Tractor',
    'Combine',
    'Sprayer',
    'Planter',
    'Tillage',
    'Truck',
    'Trailer',
    'Other',
  ];

  const statusOptions = [
    { value: 'ACTIVE', label: 'Active' },
    { value: 'INACTIVE', label: 'Inactive' },
    { value: 'MAINTENANCE', label: 'Maintenance' },
    { value: 'RETIRED', label: 'Retired' },
  ];

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>
          {isEditing ? 'Loading equipment data...' : 'Creating equipment...'}
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>
            {isEditing ? 'Edit Equipment' : 'Add New Equipment'}
          </Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Name *</Text>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={equipment.name}
              onChangeText={(value) => handleChange('name', value)}
              placeholder="Enter equipment name"
            />
            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Type *</Text>
            <View style={[styles.pickerContainer, errors.type && styles.inputError]}>
              <Picker
                selectedValue={equipment.type}
                onValueChange={(value) => handleChange('type', value)}
                style={styles.picker}
              >
                {equipmentTypes.map((type) => (
                  <Picker.Item key={type} label={type} value={type} />
                ))}
              </Picker>
            </View>
            {errors.type && <Text style={styles.errorText}>{errors.type}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Make</Text>
            <TextInput
              style={styles.input}
              value={equipment.make}
              onChangeText={(value) => handleChange('make', value)}
              placeholder="Enter make"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Model</Text>
            <TextInput
              style={styles.input}
              value={equipment.model}
              onChangeText={(value) => handleChange('model', value)}
              placeholder="Enter model"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Year</Text>
            <TextInput
              style={[styles.input, errors.year && styles.inputError]}
              value={equipment.year}
              onChangeText={(value) => handleChange('year', value)}
              placeholder="YYYY"
              keyboardType="numeric"
              maxLength={4}
            />
            {errors.year && <Text style={styles.errorText}>{errors.year}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Serial Number</Text>
            <TextInput
              style={styles.input}
              value={equipment.serialNumber}
              onChangeText={(value) => handleChange('serialNumber', value)}
              placeholder="Enter serial number"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Purchase Date</Text>
            <TextInput
              style={[styles.input, errors.purchaseDate && styles.inputError]}
              value={equipment.purchaseDate}
              onChangeText={(value) => handleChange('purchaseDate', value)}
              placeholder="YYYY-MM-DD"
            />
            {errors.purchaseDate && <Text style={styles.errorText}>{errors.purchaseDate}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Purchase Price</Text>
            <TextInput
              style={[styles.input, errors.purchasePrice && styles.inputError]}
              value={equipment.purchasePrice}
              onChangeText={(value) => handleChange('purchasePrice', value)}
              placeholder="Enter purchase price"
              keyboardType="numeric"
            />
            {errors.purchasePrice && <Text style={styles.errorText}>{errors.purchasePrice}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Current Value</Text>
            <TextInput
              style={[styles.input, errors.currentValue && styles.inputError]}
              value={equipment.currentValue}
              onChangeText={(value) => handleChange('currentValue', value)}
              placeholder="Enter current value"
              keyboardType="numeric"
            />
            {errors.currentValue && <Text style={styles.errorText}>{errors.currentValue}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={equipment.status}
                onValueChange={(value) => handleChange('status', value)}
                style={styles.picker}
              >
                {statusOptions.map((option) => (
                  <Picker.Item key={option.value} label={option.label} value={option.value} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={equipment.notes}
              onChangeText={(value) => handleChange('notes', value)}
              placeholder="Enter notes about this equipment"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Text style={styles.submitButtonText}>
                {isEditing ? 'Update Equipment' : 'Add Equipment'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 5,
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#22c55e',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default EquipmentFormScreen;
