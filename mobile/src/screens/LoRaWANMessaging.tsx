import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import loraWanService, { LoRaWANMessage, NearbyFarmer } from '../services/loraWanService';

// Tab enum for the different tabs in the screen
enum Tab {
  NEARBY = 'nearby',
  MESSAGES = 'messages',
}

const LoRaWANMessaging: React.FC = () => {
  const navigation = useNavigation();
  const [initialized, setInitialized] = useState(false);
  const [activeTab, setActiveTab] = useState<Tab>(Tab.NEARBY);
  const [nearbyFarmers, setNearbyFarmers] = useState<NearbyFarmer[]>([]);
  const [messages, setMessages] = useState<LoRaWANMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [selectedFarmer, setSelectedFarmer] = useState<NearbyFarmer | null>(null);
  const [messageType, setMessageType] = useState<'direct' | 'broadcast' | 'relay'>('broadcast');

  // Initialize the LoRaWAN service
  useEffect(() => {
    const initService = async () => {
      try {
        // Use a hardcoded user ID and name for now
        // In a real app, this would come from the user's profile
        await loraWanService.initialize('user123', 'Current User');
        setInitialized(true);
      } catch (error) {
        console.error('Error initializing LoRaWAN service:', error);
        Alert.alert('Error', 'Failed to initialize LoRaWAN service');
      }
    };

    initService();

    // Set up listeners for nearby farmers and messages
    loraWanService.onNearbyFarmersChange((farmers) => {
      setNearbyFarmers(farmers);
    });

    loraWanService.onMessage((message) => {
      setMessages((prevMessages) => [message, ...prevMessages]);
    });

    // Clean up on unmount
    return () => {
      loraWanService.destroy();
    };
  }, []);

  // Send a message
  const sendMessage = async () => {
    if (!messageText.trim()) {
      Alert.alert('Error', 'Please enter a message');
      return;
    }

    try {
      let sentMessage: LoRaWANMessage;

      switch (messageType) {
        case 'direct':
          if (!selectedFarmer) {
            Alert.alert('Error', 'Please select a farmer to send a direct message');
            return;
          }
          sentMessage = await loraWanService.sendDirectMessage(selectedFarmer.id, messageText);
          break;
        case 'broadcast':
          sentMessage = await loraWanService.sendBroadcastMessage(messageText);
          break;
        case 'relay':
          sentMessage = await loraWanService.sendRelayMessage(messageText);
          break;
      }

      // Add the sent message to the messages list
      setMessages((prevMessages) => [sentMessage, ...prevMessages]);
      
      // Clear the message text
      setMessageText('');
      
      // If it was a direct message, clear the selected farmer
      if (messageType === 'direct') {
        setSelectedFarmer(null);
        setMessageType('broadcast');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  // Select a farmer for direct messaging
  const selectFarmer = (farmer: NearbyFarmer) => {
    setSelectedFarmer(farmer);
    setMessageType('direct');
    Alert.alert(
      'Send Direct Message',
      `Send a direct message to ${farmer.name}?`,
      [
        {
          text: 'Cancel',
          onPress: () => {
            setSelectedFarmer(null);
            setMessageType('broadcast');
          },
          style: 'cancel',
        },
        { text: 'OK' },
      ]
    );
  };

  // Format timestamp to readable date/time
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Format distance to readable string
  const formatDistance = (distance?: number): string => {
    if (distance === undefined) {
      return 'Unknown distance';
    }
    
    if (distance < 1000) {
      return `${Math.round(distance)}m away`;
    } else {
      return `${(distance / 1000).toFixed(1)}km away`;
    }
  };

  // Render a nearby farmer item
  const renderFarmerItem = ({ item }: { item: NearbyFarmer }) => (
    <TouchableOpacity style={styles.farmerItem} onPress={() => selectFarmer(item)}>
      <View style={styles.farmerHeader}>
        <Text style={styles.farmerName}>{item.name}</Text>
        <View style={[styles.signalIndicator, { backgroundColor: getSignalColor(item.signalStrength) }]} />
      </View>
      <Text style={styles.farmerDistance}>{formatDistance(item.distance)}</Text>
      <Text style={styles.farmerLastSeen}>Last seen: {formatTimestamp(item.lastSeen)}</Text>
      <TouchableOpacity style={styles.messageButton} onPress={() => selectFarmer(item)}>
        <Ionicons name="chatbubble-outline" size={16} color="white" />
        <Text style={styles.messageButtonText}>Message</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  // Render a message item
  const renderMessageItem = ({ item }: { item: LoRaWANMessage }) => (
    <View style={[styles.messageItem, item.senderId === 'user123' ? styles.sentMessage : styles.receivedMessage]}>
      <View style={styles.messageHeader}>
        <Text style={styles.messageSender}>{item.senderId === 'user123' ? 'You' : item.senderName}</Text>
        <Text style={styles.messageType}>{item.messageType.toUpperCase()}</Text>
      </View>
      <Text style={styles.messageContent}>{item.content}</Text>
      <Text style={styles.messageTimestamp}>{formatTimestamp(item.timestamp)}</Text>
      {item.messageType === 'relay' && item.relayedBy && item.relayedBy.length > 0 && (
        <Text style={styles.relayInfo}>Relayed by {item.relayedBy.length} node(s)</Text>
      )}
    </View>
  );

  // Get color based on signal strength
  const getSignalColor = (strength: number): string => {
    if (strength >= 80) {
      return '#4CAF50'; // Green
    } else if (strength >= 50) {
      return '#FFC107'; // Yellow
    } else {
      return '#F44336'; // Red
    }
  };

  // If not initialized, show loading indicator
  if (!initialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0066cc" />
        <Text style={styles.loadingText}>Initializing LoRaWAN...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>LoRaWAN Messaging</Text>
      </View>

      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === Tab.NEARBY && styles.activeTab]}
          onPress={() => setActiveTab(Tab.NEARBY)}
        >
          <Text style={[styles.tabText, activeTab === Tab.NEARBY && styles.activeTabText]}>Nearby Farmers</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === Tab.MESSAGES && styles.activeTab]}
          onPress={() => setActiveTab(Tab.MESSAGES)}
        >
          <Text style={[styles.tabText, activeTab === Tab.MESSAGES && styles.activeTabText]}>Messages</Text>
        </TouchableOpacity>
      </View>

      {activeTab === Tab.NEARBY && (
        <View style={styles.content}>
          {nearbyFarmers.length === 0 ? (
            <Text style={styles.emptyText}>No nearby farmers found</Text>
          ) : (
            <FlatList
              data={nearbyFarmers}
              renderItem={renderFarmerItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.list}
            />
          )}
        </View>
      )}

      {activeTab === Tab.MESSAGES && (
        <View style={styles.content}>
          {messages.length === 0 ? (
            <Text style={styles.emptyText}>No messages yet</Text>
          ) : (
            <FlatList
              data={messages}
              renderItem={renderMessageItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.list}
            />
          )}
        </View>
      )}

      <View style={styles.messageInputContainer}>
        {selectedFarmer && (
          <View style={styles.selectedFarmerContainer}>
            <Text style={styles.selectedFarmerText}>To: {selectedFarmer.name}</Text>
            <TouchableOpacity
              onPress={() => {
                setSelectedFarmer(null);
                setMessageType('broadcast');
              }}
            >
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          </View>
        )}
        
        <View style={styles.messageTypeContainer}>
          <TouchableOpacity
            style={[styles.messageTypeButton, messageType === 'broadcast' && styles.activeMessageType]}
            onPress={() => {
              setMessageType('broadcast');
              setSelectedFarmer(null);
            }}
          >
            <Text style={styles.messageTypeText}>Broadcast</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.messageTypeButton, messageType === 'relay' && styles.activeMessageType]}
            onPress={() => {
              setMessageType('relay');
              setSelectedFarmer(null);
            }}
          >
            <Text style={styles.messageTypeText}>Relay</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.inputRow}>
          <TextInput
            style={styles.messageInput}
            value={messageText}
            onChangeText={setMessageText}
            placeholder="Type your message..."
            multiline
          />
          <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
            <Ionicons name="send" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  header: {
    backgroundColor: '#0066cc',
    paddingTop: 40,
    paddingBottom: 10,
    paddingHorizontal: 20,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#0066cc',
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: 'white',
  },
  tabText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
  },
  activeTabText: {
    color: 'white',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  list: {
    padding: 10,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: '#666',
  },
  farmerItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  farmerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  farmerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  signalIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  farmerDistance: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  farmerLastSeen: {
    fontSize: 12,
    color: '#999',
    marginBottom: 10,
  },
  messageButton: {
    backgroundColor: '#0066cc',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  messageButtonText: {
    color: 'white',
    marginLeft: 5,
    fontSize: 14,
  },
  messageItem: {
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    maxWidth: '80%',
  },
  sentMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#DCF8C6',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
    backgroundColor: 'white',
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  messageSender: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  messageType: {
    fontSize: 10,
    color: '#666',
    backgroundColor: '#eee',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  messageContent: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  messageTimestamp: {
    fontSize: 12,
    color: '#999',
    alignSelf: 'flex-end',
  },
  relayInfo: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 5,
  },
  messageInputContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    padding: 10,
  },
  selectedFarmerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 10,
  },
  selectedFarmerText: {
    fontSize: 14,
    color: '#333',
  },
  messageTypeContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  messageTypeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  activeMessageType: {
    backgroundColor: '#0066cc',
  },
  messageTypeText: {
    fontSize: 14,
    color: '#333',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageInput: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#0066cc',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
});

export default LoRaWANMessaging;