import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runInvoiceDocumentsMigration() {
  try {
    console.log('Creating invoice_documents table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/migrations/create_invoice_documents_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Invoice documents migration completed successfully');
  } catch (error) {
    console.error('Error running invoice documents migration:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runInvoiceDocumentsMigration();
