import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import DocumentFolder from '../models/DocumentFolder.js';
import Document from '../models/Document.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import { uuid_nil } from '../utils/uuidUtils.js';
import { deleteFile, updateStorageUsage, getFolderPath } from '../utils/fileUtils.js';
import { createFolderInSpaces, deleteFolderInSpaces } from '../utils/spacesUtils.js';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper function to check if a user has access to a farm
const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

// Helper function to get folder ID or root folder ID
const getFolderIdOrRoot = async (farmId, folderId) => {
  if (folderId && folderId !== "null") {
    return folderId;
  }

  // Find the root folder for this farm
  const rootFolder = await DocumentFolder.findOne({
    where: {
      farm_id: farmId,
      parent_folder_id: uuid_nil()
    }
  });

  // If root folder exists, use its ID, otherwise use uuid_nil()
  return rootFolder ? rootFolder.id : uuid_nil();
};

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

/**
 * Get all folders for a farm
 */
export const getAllFolders = async (req, res) => {
  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { parentFolderId, search } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId }; // Changed from tenant_id to farm_id

    where.parent_folder_id = await getFolderIdOrRoot(farmId, parentFolderId);

    // Add search condition if provided
    if (search) {
      where.name = { [Op.iLike]: `%${search}%` };
    }

    // Get folders
    const folders = await DocumentFolder.findAll({
      where,
      include: [
        {
          model: User,
          as: 'folderCreator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DocumentFolder,
          as: 'parentFolder',
          attributes: ['id', 'name']
        }
      ],
      order: [['name', 'ASC']]
    });

    return res.status(200).json(folders);
  } catch (error) {
    console.error('Error getting folders:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a folder by ID
 */
export const getFolderById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get folder with associations
    const folder = await DocumentFolder.findByPk(id, {
      include: [
        {
          model: User,
          as: 'folderCreator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DocumentFolder,
          as: 'parentFolder',
          attributes: ['id', 'name']
        },
        {
          model: DocumentFolder,
          as: 'subFolders',
          attributes: ['id', 'name', 'description', 'created_at']
        },
        {
          model: Document,
          as: 'folderDocuments',
          attributes: ['id', 'name', 'description', 'file_type', 'file_size', 'created_at', 'is_external', 'external_source']
        }
      ]
    });

    if (!folder) {
      return res.status(404).json({ error: 'Folder not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, folder.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this folder' });
    }

    return res.status(200).json(folder);
  } catch (error) {
    console.error('Error getting folder:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new folder
 */
export const createFolder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params; // Changed from tenantId to farmId
    const { name, description, parentFolderId } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Validate required fields
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Folder name is required' });
    }

    // Check if parent folder exists if provided
    if (parentFolderId) {
      const parentFolder = await DocumentFolder.findOne({
        where: {
          id: parentFolderId,
          farm_id: farmId // Changed from tenant_id to farm_id
        }
      });

      if (!parentFolder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Parent folder not found' });
      }
    }

    // Get the parent folder ID
    const parentId = await getFolderIdOrRoot(farmId, parentFolderId);

    // Create folder in database
    const folder = await DocumentFolder.create({
      name,
      description: description || '',
      parent_folder_id: parentId,
      farm_id: farmId, // Changed from tenant_id to farm_id
      created_by: req.user.id
    }, { transaction });

    await transaction.commit();

    // After successful database transaction, create the folder in Digital Ocean Spaces
    try {
      // Get the full path of the folder
      const folderPath = await getFolderPath(farmId, folder.id);

      // Create the folder in Spaces
      const spacesResult = await createFolderInSpaces(folderPath);

      if (!spacesResult) {
        console.error(`Failed to create folder in Spaces: ${folderPath}`);
      }
    } catch (spacesError) {
      // Log the error but don't fail the request, as the folder was created in the database
      console.error('Error creating folder in Spaces:', spacesError);
    }

    return res.status(201).json({
      message: 'Folder created successfully',
      folder
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating folder:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a folder
 */
export const updateFolder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { name, description, parentFolderId } = req.body;

    // Get folder
    const folder = await DocumentFolder.findByPk(id);

    if (!folder) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Folder not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, folder.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this folder' });
    }

    // Check if parent folder exists if provided
    if (parentFolderId) {
      // Prevent circular references
      if (parentFolderId === id) {
        await transaction.rollback();
        return res.status(400).json({ error: 'A folder cannot be its own parent' });
      }

      const parentFolder = await DocumentFolder.findOne({
        where: {
          id: parentFolderId,
          farm_id: folder.farm_id // Changed from tenant_id to farm_id
        }
      });

      if (!parentFolder) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Parent folder not found' });
      }

      // Check if the new parent is a descendant of this folder (would create a cycle)
      let currentFolder = parentFolder;
      while (currentFolder.parent_folder_id) {
        if (currentFolder.parent_folder_id === id) {
          await transaction.rollback();
          return res.status(400).json({ error: 'Cannot move a folder to its own descendant' });
        }

        currentFolder = await DocumentFolder.findByPk(currentFolder.parent_folder_id);
        if (!currentFolder) break;
      }
    }

    // Store the old path before updating
    const oldFolderPath = await getFolderPath(folder.farm_id, folder.id);
    const oldParentId = folder.parent_folder_id;

    // Update folder
    const updates = {};

    if (name) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (parentFolderId !== undefined) {
      // Use getFolderIdOrRoot to handle null or root folder
      updates.parent_folder_id = await getFolderIdOrRoot(folder.farm_id, parentFolderId);
    }

    await folder.update(updates, { transaction });

    await transaction.commit();

    // After successful database transaction, update the folder in Digital Ocean Spaces if needed
    try {
      // If the parent folder changed or the name changed, we need to update the folder in Spaces
      if (parentFolderId !== undefined || name) {
        // Get the new path
        const newFolderPath = await getFolderPath(folder.farm_id, folder.id);

        // Create the folder at the new path
        const createResult = await createFolderInSpaces(newFolderPath);

        if (!createResult) {
          console.error(`Failed to create folder at new path in Spaces: ${newFolderPath}`);
        }

        // If the parent folder changed, we should also ensure all subfolders are properly created
        if (parentFolderId !== undefined && parentFolderId !== oldParentId) {
          // Get all subfolders
          const subFolders = await DocumentFolder.findAll({
            where: { parent_folder_id: folder.id }
          });

          // Recursively create all subfolders in Spaces
          for (const subFolder of subFolders) {
            const subFolderPath = await getFolderPath(folder.farm_id, subFolder.id);
            await createFolderInSpaces(subFolderPath);
          }
        }
      }
    } catch (spacesError) {
      // Log the error but don't fail the request, as the folder was updated in the database
      console.error('Error updating folder in Spaces:', spacesError);
    }

    return res.status(200).json({
      message: 'Folder updated successfully',
      folder
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating folder:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a folder
 */
export const deleteFolder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { recursive = false } = req.query;

    // Get folder
    const folder = await DocumentFolder.findByPk(id, {
      include: [
        {
          model: DocumentFolder,
          as: 'subFolders'
        },
        {
          model: Document,
          as: 'folderDocuments'
        }
      ]
    });

    if (!folder) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Folder not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, folder.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this folder' });
    }

    // Get the folder path before deleting
    const folderPath = await getFolderPath(folder.farm_id, folder.id);

    // Check if folder is empty
    if ((folder.subFolders && folder.subFolders.length > 0) ||
        (folder.folderDocuments && folder.folderDocuments.length > 0)) {

      if (!recursive) {
        await transaction.rollback();
        return res.status(400).json({
          error: 'Folder is not empty. Set recursive=true to delete all contents.'
        });
      }

      // Recursively delete all contents
      await recursivelyDeleteFolder(folder, req.user.id, transaction);
    } else {
      // Just delete the empty folder
      await folder.destroy({ transaction });
    }

    await transaction.commit();

    // After successful database transaction, delete the folder from Digital Ocean Spaces
    try {
      // Delete the folder from Spaces
      const deleteResult = await deleteFolderInSpaces(folderPath);

      if (!deleteResult) {
        console.error(`Failed to delete folder from Spaces: ${folderPath}`);
      }
    } catch (spacesError) {
      // Log the error but don't fail the request, as the folder was deleted from the database
      console.error('Error deleting folder from Spaces:', spacesError);
    }

    return res.status(200).json({
      message: 'Folder deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting folder:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Recursively delete a folder and all its contents
 * @param {Object} folder - The folder to delete
 * @param {string} userId - The user ID performing the deletion
 * @param {Object} transaction - The database transaction
 */
const recursivelyDeleteFolder = async (folder, userId, transaction) => {
  // First, recursively delete all subfolders
  if (folder.subFolders && folder.subFolders.length > 0) {
    for (const subFolder of folder.subFolders) {
      // Get the full subfolder with its contents
      const fullSubFolder = await DocumentFolder.findByPk(subFolder.id, {
        include: [
          {
            model: DocumentFolder,
            as: 'subFolders'
          },
          {
            model: Document,
            as: 'folderDocuments'
          }
        ],
        transaction
      });

      await recursivelyDeleteFolder(fullSubFolder, userId, transaction);
    }
  }

  // Then, delete all documents in this folder
  if (folder.folderDocuments && folder.folderDocuments.length > 0) {
    for (const document of folder.folderDocuments) {
      // Delete file from disk if it's a local file
      if (!document.is_external) {
        await deleteFile(document.file_path);
      }

      // Update storage usage
      await updateStorageUsage(
        folder.farm_id, // Changed from tenant_id to farm_id
        document.is_external ? 0 : -document.file_size,
        document.is_external,
        -1
      );

      // Delete document record
      await document.destroy({ transaction });
    }
  }

  // Get the folder path before deleting
  try {
    const folderPath = await getFolderPath(folder.farm_id, folder.id);

    // Delete the folder marker from Digital Ocean Spaces
    // We do this outside the transaction because it's not critical for database consistency
    // and we don't want to roll back the database transaction if this fails
    setTimeout(async () => {
      try {
        await deleteFolderInSpaces(folderPath);
      } catch (error) {
        console.error(`Error deleting folder from Spaces after transaction: ${folderPath}`, error);
      }
    }, 0);
  } catch (error) {
    console.error('Error getting folder path for deletion from Spaces:', error);
  }

  // Finally, delete the folder itself
  await folder.destroy({ transaction });
};

/**
 * Get folder hierarchy
 */
export const getFolderHierarchy = async (req, res) => {
  try {
    const { farmId } = req.params; // Changed from tenantId to farmId

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Get all folders for the farm
    const allFolders = await DocumentFolder.findAll({
      where: { farm_id: farmId }, // Changed from tenant_id to farm_id
      attributes: ['id', 'name', 'parent_folder_id', 'is_secure'],
      order: [['name', 'ASC']]
    });

    // Build hierarchy
    const rootFolders = [];
    const folderMap = {};

    // First, create a map of all folders
    allFolders.forEach(folder => {
      folderMap[folder.id] = {
        id: folder.id,
        name: folder.name,
        children: [],
        is_secure: folder.is_secure
      };
    });

    // Then, build the hierarchy
    allFolders.forEach(folder => {
      if (folder.parent_folder_id && folder.parent_folder_id !== uuid_nil()) {
        // This is a child folder (not at root level)
        if (folderMap[folder.parent_folder_id]) {
          folderMap[folder.parent_folder_id].children.push(folderMap[folder.id]);
        }
      } else {
        // This is a root folder (either parent_folder_id is null or uuid_nil)
        rootFolders.push(folderMap[folder.id]);
      }
    });

    return res.status(200).json(rootFolders);
  } catch (error) {
    console.error('Error getting folder hierarchy:', error);
    return res.status(500).json({ error: error.message });
  }
};
