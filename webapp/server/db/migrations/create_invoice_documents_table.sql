-- Migration: Create invoice_documents table for multiple document support
-- Depends on: add_invoice_documents.sql

SET search_path TO site;

-- Create the invoice_documents table
CREATE TABLE IF NOT EXISTS invoice_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_path VARCHAR(1024) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_invoice_documents_invoice_id ON invoice_documents(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_documents_uploaded_by ON invoice_documents(uploaded_by);

-- Add comments
COMMENT ON TABLE invoice_documents IS 'Stores multiple documents associated with invoices';
COMMENT ON COLUMN invoice_documents.invoice_id IS 'The invoice this document belongs to';
COMMENT ON COLUMN invoice_documents.name IS 'Original filename of the uploaded document';
COMMENT ON COLUMN invoice_documents.description IS 'Optional description of the document';
COMMENT ON COLUMN invoice_documents.file_path IS 'Storage path in Digital Ocean Spaces';
COMMENT ON COLUMN invoice_documents.file_size IS 'Size of the document in bytes';
COMMENT ON COLUMN invoice_documents.mime_type IS 'MIME type of the document (e.g., application/pdf, text/plain)';
COMMENT ON COLUMN invoice_documents.uploaded_by IS 'User who uploaded the document';