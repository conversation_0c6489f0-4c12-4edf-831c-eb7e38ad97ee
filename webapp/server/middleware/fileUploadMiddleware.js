import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import spacesStorage from './spacesStorageEngine.js';

// Define allowed file types
const ALLOWED_FILE_TYPES = {
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/webp': 'webp',
  'application/pdf': 'pdf',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'text/plain': 'txt',
  'text/csv': 'csv'
};

// Define allowed file types for invoice documents (only PDF and text files)
const ALLOWED_INVOICE_DOCUMENT_TYPES = {
  'application/pdf': 'pdf',
  'text/plain': 'txt'
};

// Maximum file size (10MB in bytes)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Configure storage for support ticket attachments using Digital Ocean Spaces
const supportTicketStorage = spacesStorage({
  destination: async (req, file, cb) => {
    try {
      // Get the ticket ID from the request parameters
      const ticketId = req.params.ticketId || 'temp';

      // Create a folder structure for support tickets
      // If we have user information, create a user-specific folder
      let userFolder = 'unknown-user';

      if (req.user && req.user.id) {
        try {
          // Import User model dynamically to avoid circular dependencies
          const User = (await import('../models/User.js')).default;

          // Find the user
          const user = await User.findByPk(req.user.id, {
            attributes: ['id', 'email', 'first_name', 'last_name']
          });

          if (user) {
            // Sanitize user information for use as folder name
            const userName = user.first_name && user.last_name 
              ? `${user.first_name}_${user.last_name}` 
              : user.email.split('@')[0];
            userFolder = `${user.id}-${userName.replace(/[^a-zA-Z0-9-_]/g, '_')}`;
          }
        } catch (error) {
          console.error(`Error getting user details for folder structure: ${error.message}`);
          // Continue with default folder name if there's an error
        }
      }

      // Create the storage path for Digital Ocean Spaces: support-tickets/[user]
      const storagePath = path.join('support-tickets', userFolder).replace(/\\/g, '/');
      console.log(`Generated storage path for Digital Ocean Spaces: ${storagePath}`);

      cb(null, storagePath);
    } catch (error) {
      console.error(`Error creating storage path: ${error.message}`);
      cb(new Error(`Failed to create storage path: ${error.message}`));
    }
  },
  filename: (req, file, cb) => {
    try {
      const ticketId = req.params.ticketId || 'temp';
      const fileExtension = path.extname(file.originalname) || '';
      const uniqueFilename = `${ticketId}_${uuidv4()}${fileExtension}`;
      console.log(`Generated filename for Digital Ocean Spaces: ${uniqueFilename} for file: ${file.originalname}`);
      cb(null, uniqueFilename);
    } catch (error) {
      console.error(`Error generating filename: ${error.message}`);
      cb(new Error(`Failed to generate filename: ${error.message}`));
    }
  }
});

// File filter to validate file types
const fileFilter = (req, file, cb) => {
  // Check if the file type is allowed
  if (ALLOWED_FILE_TYPES[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, documents, and PDFs are allowed.'), false);
  }
};

// Configure storage for invoice documents using Digital Ocean Spaces
const invoiceDocumentStorage = spacesStorage({
  destination: async (req, file, cb) => {
    try {
      // Get the invoice ID from the request parameters
      const invoiceId = req.params.invoiceId || 'temp';

      // If we have a valid invoice ID, get the farm information
      let farmId = 'unknown-farm';
      let userId = req.user ? req.user.id : null;

      if (invoiceId && invoiceId !== 'temp') {
        try {
          // Import models dynamically to avoid circular dependencies
          const Invoice = (await import('../models/Invoice.js')).default;
          const Farm = (await import('../models/Farm.js')).default;

          // Find the invoice with farm information
          const invoice = await Invoice.findByPk(invoiceId, {
            include: [
              { model: Farm, attributes: ['id', 'name'] }
            ]
          });

          if (invoice && invoice.farm_id) {
            farmId = invoice.farm_id;
          }
        } catch (error) {
          console.error(`Error getting invoice details for folder structure: ${error.message}`);
          // Continue with default folder name if there's an error
        }
      }

      // Import the generateStoragePath function from fileUtils
      const { generateStoragePath } = await import('../utils/fileUtils.js');

      // Generate a storage path using the same logic as documents, but with 'invoices' folder
      // We'll modify the path after generation to replace 'documents' with 'invoices'
      const originalPath = await generateStoragePath(farmId, userId, file.originalname, null);

      // Replace 'documents' with 'invoices' in the path
      const storagePath = originalPath.replace('/documents/', '/invoices/');
      console.log(`Generated storage path for invoice document: ${storagePath}`);

      // Store the full storage path in the request object for use in the filename function
      req.storagePath = storagePath;

      // Extract just the directory part for the destination
      const lastSlashIndex = storagePath.lastIndexOf('/');
      const dirPath = lastSlashIndex !== -1 ? storagePath.substring(0, lastSlashIndex) : '';
      console.log(`Directory path for storage: ${dirPath}`);

      // Import createFolderInSpaces function to ensure the folder exists
      try {
        const { createFolderInSpaces } = await import('../utils/spacesUtils.js');
        await createFolderInSpaces(dirPath);
        console.log(`Ensured folder exists in Spaces: ${dirPath}`);
      } catch (folderError) {
        console.error(`Error ensuring folder exists: ${folderError.message}`);
        // Continue even if folder creation fails, as the upload might still succeed
      }

      cb(null, dirPath);
    } catch (error) {
      console.error(`Error creating storage path: ${error.message}`);
      cb(new Error(`Failed to create storage path: ${error.message}`));
    }
  },
  filename: (req, file, cb) => {
    try {
      // We don't need to generate a filename here because the full path including filename
      // is already generated by generateStoragePath in the destination function
      // Just pass the last part of the storagePath as the filename
      const storagePath = req.storagePath;
      const filename = storagePath ? storagePath.split('/').pop() : `invoice_${uuidv4()}${path.extname(file.originalname)}`;

      console.log(`Using filename from storage path: ${filename} for file: ${file.originalname}`);
      cb(null, filename);
    } catch (error) {
      console.error(`Error generating filename: ${error.message}`);
      cb(new Error(`Failed to generate filename: ${error.message}`));
    }
  }
});

// File filter for invoice documents (only PDF and text files)
const invoiceDocumentFileFilter = (req, file, cb) => {
  // Check if the file type is allowed for invoice documents
  if (ALLOWED_INVOICE_DOCUMENT_TYPES[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only PDF and text files are allowed for invoice documents.'), false);
  }
};

// Create multer upload instance with size limits and file type validation
export const uploadSupportTicketFile = multer({
  storage: supportTicketStorage,
  limits: {
    fileSize: MAX_FILE_SIZE // 10MB
  },
  fileFilter: fileFilter
});

// Create multer upload instance for invoice documents
export const uploadInvoiceDocument = multer({
  storage: invoiceDocumentStorage,
  limits: {
    fileSize: MAX_FILE_SIZE // 10MB - keep it simple like support tickets
  },
  fileFilter: invoiceDocumentFileFilter
});

// Error handling middleware for multer errors
export const handleFileUploadErrors = (err, req, res, next) => {
  console.log('File upload middleware error handler triggered');

  if (err instanceof multer.MulterError) {
    console.error(`Multer error: ${err.code} - ${err.message}`);

    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File size exceeds the limit of 10MB.'
      });
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Unexpected field in form data. Make sure you are using the field name "document".'
      });
    } else if (err.code === 'LIMIT_PART_COUNT') {
      return res.status(400).json({
        error: 'Too many parts in the form data.'
      });
    } else if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files. Only one file is allowed.'
      });
    } else if (err.code === 'LIMIT_FIELD_SIZE') {
      return res.status(400).json({
        error: 'Field size exceeds the limit. Please try with a smaller file.'
      });
    }

    return res.status(400).json({
      error: `File upload error: ${err.message}`
    });
  } else if (err) {
    console.error(`Non-Multer error during file upload: ${err.message}`);

    // Handle specific case for "Unexpected end of form" error or timeout
    if (err.message === 'Unexpected end of form' || 
        err.message.toLowerCase().includes('unexpected end') || 
        err.message.toLowerCase().includes('end of form') ||
        err.message.toLowerCase().includes('timeout')) {
      console.log(`Handling upload error: ${err.message}`);

      // Log additional request information for debugging
      console.log(`Request headers: ${JSON.stringify(req.headers)}`);
      console.log(`Request content type: ${req.headers['content-type']}`);
      console.log(`Request content length: ${req.headers['content-length']}`);

      // Determine if it's a timeout or form error
      const isTimeout = err.message.toLowerCase().includes('timeout');

      return res.status(400).json({
        error: isTimeout 
          ? 'File upload timed out. Please try uploading a smaller file or check your network connection.' 
          : 'File upload was interrupted or incomplete. Please try uploading again with a smaller file or check your network connection.',
        details: isTimeout
          ? 'The server timed out while processing your upload. This is often due to large file sizes or slow network connections.'
          : 'The server received an incomplete form submission. This may be due to network issues, timeouts, or file size limitations.'
      });
    }

    // Handle Digital Ocean Spaces errors
    if (err.message.includes('AccessDenied') || err.message.includes('access denied')) {
      return res.status(403).json({
        error: 'Access denied to storage service. Please contact support.'
      });
    }

    if (err.message.includes('NoSuchBucket') || err.message.includes('no such bucket')) {
      return res.status(500).json({
        error: 'Storage configuration error. Please contact support.'
      });
    }

    if (err.message.includes('NetworkingError') || err.message.includes('network error')) {
      return res.status(503).json({
        error: 'Storage service is currently unavailable. Please try again later.'
      });
    }

    return res.status(400).json({
      error: err.message
    });
  }

  console.log('File upload completed successfully, proceeding to next middleware');
  next();
};
