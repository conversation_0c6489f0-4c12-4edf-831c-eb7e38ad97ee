#!/usr/bin/env node

/**
 * Test script to verify Customer-Invoice associations work correctly
 */

import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import InvoiceItem from '../models/InvoiceItem.js';
import { setupAssociations } from '../models/associations.js';

const testAssociations = async () => {
  try {
    console.log('🔧 Setting up database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    console.log('🔧 Setting up model associations...');
    setupAssociations();
    console.log('✅ Model associations setup completed');

    console.log('🔧 Testing Customer-Invoice association...');

    // Test 1: Check if associations are properly defined
    console.log('\n📋 Test 1: Checking association definitions');
    const invoiceAssociations = Object.keys(Invoice.associations);
    const customerAssociations = Object.keys(Customer.associations);

    console.log('Invoice associations:', invoiceAssociations);
    console.log('Customer associations:', customerAssociations);

    const hasCustomerAssociation = invoiceAssociations.includes('Customer');
    const hasInvoiceAssociation = customerAssociations.includes('Invoices');

    console.log('Invoice -> Customer association exists:', hasCustomerAssociation);
    console.log('Customer -> Invoices association exists:', hasInvoiceAssociation);

    // Test 2: Try to query with associations
    console.log('\n📋 Test 2: Testing query with associations');

    try {
      const invoiceWithCustomer = await Invoice.findOne({
        include: [
          {
            model: Customer,
            as: 'Customer',
            required: false,
            attributes: ['id', 'name', 'email']
          }
        ],
        limit: 1
      });

      console.log('✅ Successfully queried Invoice with Customer association');
      if (invoiceWithCustomer) {
        console.log('Invoice ID:', invoiceWithCustomer.id);
        console.log('Customer:', invoiceWithCustomer.Customer ? invoiceWithCustomer.Customer.name : 'No customer');
      } else {
        console.log('No invoices found in database');
      }
    } catch (error) {
      console.error('❌ Error querying Invoice with Customer:', error.message);
    }

    try {
      const customerWithInvoices = await Customer.findOne({
        include: [
          {
            model: Invoice,
            required: false,
            attributes: ['id', 'invoice_number', 'status', 'total_amount']
          }
        ],
        limit: 1
      });

      console.log('✅ Successfully queried Customer with Invoices association');
      if (customerWithInvoices) {
        console.log('Customer ID:', customerWithInvoices.id);
        console.log('Customer name:', customerWithInvoices.name);
        console.log('Number of invoices:', customerWithInvoices.Invoices ? customerWithInvoices.Invoices.length : 0);
      } else {
        console.log('No customers found in database');
      }
    } catch (error) {
      console.error('❌ Error querying Customer with Invoices:', error.message);
    }

    // Test 3: Check specific invoice-customer relationship
    console.log('\n📋 Test 3: Testing specific invoice-customer relationships');

    try {
      const invoicesWithCustomers = await Invoice.findAll({
        where: {
          customer_id: { [sequelize.Op.ne]: null }
        },
        include: [
          {
            model: Customer,
            as: 'Customer',
            required: false,
            attributes: ['id', 'name', 'email']
          }
        ],
        limit: 5
      });

      console.log(`✅ Found ${invoicesWithCustomers.length} invoices with customer_id`);

      invoicesWithCustomers.forEach((invoice, index) => {
        console.log(`Invoice ${index + 1}:`);
        console.log(`  ID: ${invoice.id}`);
        console.log(`  Number: ${invoice.invoice_number}`);
        console.log(`  Customer ID: ${invoice.customer_id}`);
        console.log(`  Customer Name: ${invoice.Customer ? invoice.Customer.name : 'Association failed'}`);
      });

    } catch (error) {
      console.error('❌ Error in specific relationship test:', error.message);
    }

    console.log('\n🎉 Association test completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
    console.log('🔌 Database connection closed');
  }
};

// Run the test
testAssociations();
