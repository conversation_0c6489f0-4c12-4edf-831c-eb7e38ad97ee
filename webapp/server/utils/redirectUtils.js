/**
 * Server-side utility functions for handling redirects related to farm selection and admin pages
 */

/**
 * Gets the redirect URL for the app subdomain login page
 * @param {string} protocol - The protocol (http or https)
 * @param {string} originalUrl - The original URL to redirect back to after login
 * @param {string} [targetSubdomain] - The target subdomain to redirect to after login
 * @param {string} [error] - Optional error message to include in the redirect
 * @param {boolean} [unauthorized] - Optional flag to indicate unauthorized access
 * @returns {string} The redirect URL
 */
export const getAppSubdomainLoginRedirectUrl = (
  protocol,
  originalUrl,
  targetSubdomain,
  error,
  unauthorized
) => {
  const MAIN_DOMAIN = process.env.MAIN_DOMAIN || 'nxtacre.com';
  let redirectUrl = `${protocol}://app.${MAIN_DOMAIN}/login?redirect=${encodeURIComponent(originalUrl)}`;
  
  if (targetSubdomain) {
    redirectUrl += `&subdomain=${targetSubdomain}`;
  }
  
  if (error) {
    redirectUrl += `&error=${encodeURIComponent(error)}`;
  }
  
  if (unauthorized) {
    redirectUrl += '&unauthorized=true';
  }
  
  return redirectUrl;
};

/**
 * Handles redirects for the admin subdomain
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 * @param {object} user - User object (if authenticated)
 * @param {string} token - JWT token (if available)
 * @returns {boolean} True if a redirect was performed, false otherwise
 */
export const handleAdminSubdomainRedirect = (req, res, next, user, token) => {
  const host = req.hostname;
  
  // Only process for admin subdomain
  if (host !== 'admin.nxtacre.com') {
    return false;
  }
  
  // If no token, redirect to app subdomain for login
  if (!token) {
    const protocol = req.secure ? 'https' : 'http';
    const redirectUrl = getAppSubdomainLoginRedirectUrl(
      protocol,
      req.originalUrl,
      'admin'
    );
    res.redirect(302, redirectUrl);
    return true;
  }
  
  // If user not found or not a global admin, redirect to app subdomain
  if (!user || !user.is_global_admin) {
    const protocol = req.secure ? 'https' : 'http';
    const redirectUrl = getAppSubdomainLoginRedirectUrl(
      protocol,
      req.originalUrl,
      'admin',
      null,
      true
    );
    res.redirect(302, redirectUrl);
    return true;
  }
  
  // User is a global admin, attach to request and continue
  req.user = user;
  return false;
};

/**
 * Handles redirects for token verification failures
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Error} error - Error object from token verification
 * @returns {void}
 */
export const handleTokenVerificationFailure = (req, res, error) => {
  const protocol = req.secure ? 'https' : 'http';
  const redirectUrl = getAppSubdomainLoginRedirectUrl(
    protocol,
    req.originalUrl,
    'admin',
    error.message
  );
  res.redirect(302, redirectUrl);
};