import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import Farm from './Farm.js';

const FarmFulfillmentOptions = sequelize.define('farm_fulfillment_options', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  offers_delivery: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  offers_pickup: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  delivery_fee: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  min_order_for_free_delivery: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  delivery_radius_miles: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  pickup_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  delivery_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_fulfillment_options',
  schema: 'site',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
FarmFulfillmentOptions.associate = (models) => {
  FarmFulfillmentOptions.belongsTo(models.Farm, {
    foreignKey: 'farm_id',
    as: 'farm'
  });
};

// Ensure the association with Farm is established
FarmFulfillmentOptions.belongsTo(Farm, {
  foreignKey: 'farm_id',
  as: 'farm'
});

export default FarmFulfillmentOptions;