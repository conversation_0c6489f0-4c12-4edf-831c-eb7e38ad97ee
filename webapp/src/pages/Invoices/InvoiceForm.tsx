import { useState, useEffect, useContext, useCallback } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { format, addDays } from 'date-fns';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';

interface Customer {
  id: string;
  name: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

interface Farm {
  id: string;
  name: string;
  tax_rate?: number;
}

interface InvoiceItem {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  taxable?: boolean;
}

interface InvoiceFormData {
  farmId: string;
  customerId: string;
  recipientFarmId: string;
  recipientType: 'customer' | 'farm';
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  status: string;
  notes: string;
  items: InvoiceItem[];
  sendNotification: boolean;
  recipientEmails: string[];
}

const InvoiceForm = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const isEditMode = !!invoiceId;
  const [searchParams] = useSearchParams();
  const preselectedCustomerId = searchParams.get('customerId');

  const [formData, setFormData] = useState<InvoiceFormData>({
    farmId: '',
    customerId: preselectedCustomerId || '',
    recipientFarmId: '',
    recipientType: 'customer',
    invoiceNumber: '',
    issueDate: format(new Date(), 'yyyy-MM-dd'),
    dueDate: format(addDays(new Date(), 30), 'yyyy-MM-dd'),
    status: 'draft',
    notes: '',
    items: [{ description: '', quantity: 1, unitPrice: 0, amount: 0, taxable: true }],
    sendNotification: false,
    recipientEmails: []
  });

  const [farms, setFarms] = useState<Farm[]>([]);

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [fileUploadError, setFileUploadError] = useState<string | null>(null);
  const [fileUploadSuccess, setFileUploadSuccess] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();
  const navigate = useNavigate();

  // Add a function to handle adding a product as an item
  const addProductAsItem = (productId: string) => {
    const selectedProduct = products.find(product => product.id === productId);
    if (!selectedProduct) return;

    const newItem = {
      description: selectedProduct.name,
      quantity: 1,
      unitPrice: selectedProduct.price,
      amount: selectedProduct.price,
      productId: productId // Add the product ID to link with inventory
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));
  };

  // Add a modal state for product selection
  const [showProductModal, setShowProductModal] = useState(false);

  // Email notification state
  const [newEmail, setNewEmail] = useState('');

  // Add email to the list
  const addEmail = () => {
    if (newEmail.trim() && !formData.recipientEmails.includes(newEmail.trim())) {
      setFormData(prev => ({
        ...prev,
        recipientEmails: [...prev.recipientEmails, newEmail.trim()]
      }));
      setNewEmail('');
    }
  };

  // Remove email from the list
  const removeEmail = (email: string) => {
    setFormData(prev => ({
      ...prev,
      recipientEmails: prev.recipientEmails.filter(e => e !== email)
    }));
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Validate file type
      const allowedTypes = ['application/pdf', 'text/plain'];
      if (!allowedTypes.includes(selectedFile.type)) {
        setFileUploadError('Invalid file type. Only PDF and text files are allowed.');
        setFile(null);
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (selectedFile.size > maxSize) {
        setFileUploadError('File size exceeds 10MB limit.');
        setFile(null);
        return;
      }

      setFile(selectedFile);
      setFileUploadError(null);
      setFileUploadSuccess(null);
    }
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      // Validate file type
      const allowedTypes = ['application/pdf', 'text/plain'];
      if (!allowedTypes.includes(droppedFile.type)) {
        setFileUploadError('Invalid file type. Only PDF and text files are allowed.');
        setFile(null);
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (droppedFile.size > maxSize) {
        setFileUploadError('File size exceeds 10MB limit.');
        setFile(null);
        return;
      }

      setFile(droppedFile);
      setFileUploadError(null);
      setFileUploadSuccess(null);
    }
  }, []);


  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm) {
      if (!isEditMode) {
        setFormData(prev => ({ ...prev, farmId: selectedFarm.id }));
        // Fetch the next invoice number (only for new invoices)
        fetchNextInvoiceNumber(selectedFarm.id);
      }

      // Always fetch these regardless of edit mode
      // Fetch customers and products for the selected farm
      fetchCustomers(selectedFarm.id);
      fetchProducts(selectedFarm.id);
      // Fetch available farms for farm-to-farm invoicing
      fetchFarms();
    }
  }, [selectedFarm, isEditMode, user?.id]);

  // Fetch the next invoice number for a farm
  const fetchNextInvoiceNumber = async (farmId: string) => {
    try {
      const response = await axios.get(`${API_URL}/invoices/next-number/${farmId}`);
      if (response.data && response.data.nextInvoiceNumber) {
        setFormData(prev => ({ ...prev, invoiceNumber: response.data.nextInvoiceNumber }));
      }
    } catch (err: any) {
      console.error('Error fetching next invoice number:', err);
      // If there's an error, we'll just leave the invoice number field empty
      // and the backend will generate one when the form is submitted
    }
  };

  // Fetch customers for a farm
  const fetchCustomers = async (farmId: string) => {
    try {
      const response = await axios.get(`${API_URL}/customers/farm/${farmId}`);
      const customersList = Array.isArray(response.data) ? response.data : response.data.customers || [];
      setCustomers(customersList);
    } catch (err: any) {
      console.error('Error fetching customers:', err);
    }
  };

  // Fetch available farms for farm-to-farm invoicing
  const fetchFarms = async () => {
    try {
      if (!selectedFarm?.id) {
        console.error('No farm selected');
        return;
      }

      // Get farms associated with the current farm
      const response = await axios.get(`${API_URL}/farm-associations/farm/${selectedFarm.id}`);
      const associations = Array.isArray(response.data) ? response.data : [];

      // Get all permissions for the current farm
      const permissionsResponse = await axios.get(`${API_URL}/farm-association-permissions/farm/${selectedFarm.id}`);
      const permissions = Array.isArray(permissionsResponse.data) ? permissionsResponse.data : [];

      // Extract the associated farms from the associations that have active invoice permissions
      const associatedFarms: Farm[] = [];

      associations.forEach(association => {
        // Check if there's an active invoice permission for this association
        const hasInvoicePermission = permissions.some(permission => 
          permission.farm_association_id === association.id && 
          permission.permission_type === 'invoices' && 
          permission.status === 'active'
        );

        // Only add farms that have active invoice permissions
        if (hasInvoicePermission) {
          // If the current farm is the initiator, add the associated farm
          if (association.initiator_farm_id === selectedFarm.id && association.status === 'active') {
            associatedFarms.push({
              id: association.associatedFarm.id,
              name: association.associatedFarm.name
            });
          }
          // If the current farm is the associated farm, add the initiator farm
          else if (association.associated_farm_id === selectedFarm.id && association.status === 'active') {
            associatedFarms.push({
              id: association.initiatorFarm.id,
              name: association.initiatorFarm.name
            });
          }
        }
      });

      setFarms(associatedFarms);
    } catch (err: any) {
      console.error('Error fetching associated farms:', err);
    }
  };

  // Fetch products for a farm
  const fetchProducts = async (farmId: string) => {
    try {
      const response = await axios.get(`${API_URL}/products/farm/${farmId}`);
      const productsList = Array.isArray(response.data) ? response.data : response.data.products || [];
      setProducts(productsList);
    } catch (err: any) {
      console.error('Error fetching products:', err);
    }
  };

  // Fetch invoice data if in edit mode
  useEffect(() => {
    if (!isEditMode || !invoiceId) return;

    const fetchInvoice = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/invoices/${invoiceId}`);
        const invoice = response.data.invoice;

        // Determine recipient type based on whether customer_id or recipient_farm_id is present
        const recipientType = invoice.recipient_farm_id ? 'farm' : 'customer';

        // If we're editing an invoice, we need to fetch farms
        if (recipientType === 'farm' || selectedFarm) {
          fetchFarms();
        }

        setFormData({
          farmId: invoice.farm_id || '',
          customerId: invoice.customer_id || '',
          recipientFarmId: invoice.recipient_farm_id || '',
          recipientType,
          invoiceNumber: invoice.invoice_number || '',
          issueDate: format(new Date(invoice.issue_date), 'yyyy-MM-dd'),
          dueDate: format(new Date(invoice.due_date), 'yyyy-MM-dd'),
          status: invoice.status || 'draft',
          notes: invoice.notes || '',
          items: invoice.InvoiceItems?.map((item: any) => ({
            id: item.id,
            description: item.description || '',
            quantity: item.quantity || 0,
            unitPrice: item.unit_price || 0,
            amount: item.amount || 0,
            taxable: item.taxable !== undefined ? item.taxable : true
          })) || [{ description: '', quantity: 1, unitPrice: 0, amount: 0, taxable: true }],
          sendNotification: false,
          recipientEmails: []
        });
      } catch (err: any) {
        console.error('Error fetching invoice:', err);
        setError(err.response?.data?.error || 'Failed to load invoice details');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Fetch customer tax exempt status when customer changes
  useEffect(() => {
    if (formData.customerId && formData.recipientType === 'customer') {
      const fetchCustomerTaxStatus = async () => {
        try {
          const response = await axios.get(`${API_URL}/customers/${formData.customerId}`);
          const customer = response.data.customer;

          if (customer && customer.is_tax_exempt !== undefined) {
            // If customer is tax exempt, update all items to be non-taxable by default
            // Only update items that haven't been explicitly set by the user
            if (customer.is_tax_exempt) {
              setFormData(prev => ({
                ...prev,
                items: prev.items.map(item => ({
                  ...item,
                  // Only update if taxable hasn't been explicitly toggled
                  taxable: item.taxable === undefined ? false : item.taxable
                }))
              }));
            }
          }
        } catch (err) {
          console.error('Error fetching customer tax status:', err);
        }
      };

      fetchCustomerTaxStatus();
    }
  }, [formData.customerId, formData.recipientType]);

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: string | number | boolean) => {
    const newItems = [...formData.items];

    if (field === 'quantity' || field === 'unitPrice') {
      const numValue = parseFloat(value as string) || 0;
      newItems[index][field] = numValue;

      // Recalculate amount
      const quantity = field === 'quantity' ? numValue : newItems[index].quantity;
      const unitPrice = field === 'unitPrice' ? numValue : newItems[index].unitPrice;
      newItems[index].amount = quantity * unitPrice;
    } else if (field === 'description') {
      // Handle string fields
      newItems[index][field] = value as string;
    } else if (field === 'amount') {
      // Handle number fields - ensure we only assign numbers
      const numValue = typeof value === 'string' ? parseFloat(value) || 0 : typeof value === 'number' ? value : 0;
      newItems[index][field] = numValue;
    } else if (field === 'id' && typeof value === 'string') {
      // Handle optional id field
      newItems[index][field] = value;
    } else if (field === 'taxable') {
      // Handle boolean fields
      newItems[index][field] = value as boolean;
    }

    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    // Default taxable to true (most items are taxable)
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, amount: 0, taxable: true }]
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length === 1) {
      return; // Don't remove the last item
    }

    const newItems = [...formData.items];
    newItems.splice(index, 1);
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const calculateSubtotal = () => {
    return formData.items.reduce((sum, item) => sum + item.amount, 0);
  };

  const calculateTax = () => {
    // Get the tax rate from the selected farm
    const taxRate = selectedFarm?.tax_rate || 0;

    // Calculate tax only on taxable items
    const taxableAmount = formData.items.reduce((sum, item) => {
      // If item is taxable (or undefined, default to taxable)
      if (item.taxable !== false) {
        return sum + item.amount;
      }
      return sum;
    }, 0);

    return taxableAmount * (taxRate / 100);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate form
    if (!formData.farmId) {
      setError('Please select a farm');
      setLoading(false);
      return;
    }

    if (formData.recipientType === 'customer' && !formData.customerId) {
      setError('Please select a customer');
      setLoading(false);
      return;
    }

    if (formData.recipientType === 'farm' && !formData.recipientFarmId) {
      setError('Please select a recipient farm');
      setLoading(false);
      return;
    }

    // Invoice number is now optional as it will be auto-generated if not provided

    if (formData.items.some(item => !item.description || item.quantity <= 0)) {
      setError('Please fill in all item details and ensure quantities are greater than zero');
      setLoading(false);
      return;
    }

    try {
      const payload: {
        farmId: string;
        invoiceNumber: string;
        issueDate: string;
        dueDate: string;
        status: string;
        notes: string;
        items: {
          description: string;
          quantity: number;
          unitPrice: number;
          amount: number;
          taxable: boolean;
          productId: any;
        }[];
        taxAmount: number;
        updateInventory: boolean;
        customerId?: string;
        recipientFarmId?: string;
      } = {
        farmId: formData.farmId,
        invoiceNumber: formData.invoiceNumber,
        issueDate: formData.issueDate,
        dueDate: formData.dueDate,
        status: formData.status,
        notes: formData.notes,
        items: formData.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.amount,
          taxable: item.taxable !== undefined ? item.taxable : true, // Include taxable status
          productId: (item as any).productId // Include product ID if available
        })),
        taxAmount: calculateTax(),
        updateInventory: formData.status === 'paid' || formData.status === 'completed' // Only update inventory for paid or completed invoices
      };

      // Add either customerId or recipientFarmId based on the recipient type
      if (formData.recipientType === 'customer') {
        payload.customerId = formData.customerId;
      } else {
        payload.recipientFarmId = formData.recipientFarmId;
      }

      let createdInvoiceId;

      if (isEditMode) {
        // Update existing invoice
        await axios.put(`${API_URL}/invoices/${invoiceId}`, payload);
        setSuccess('Invoice updated successfully');
        createdInvoiceId = invoiceId;
      } else {
        // Create new invoice
        const response = await axios.post(`${API_URL}/invoices`, payload);
        setSuccess('Invoice created successfully');
        createdInvoiceId = response.data.invoice.id;
      }

      // Send notification if checkbox is checked
      if (formData.sendNotification && createdInvoiceId) {
        try {
          // Determine the recipient emails
          const emails = formData.recipientEmails.length > 0 ? formData.recipientEmails : [];

          // Send the invoice notification
          await axios.post(`${API_URL}/invoices/${createdInvoiceId}/send`, {
            recipientEmails: emails
          });

          setSuccess(isEditMode ? 
            'Invoice updated and notification sent successfully' : 
            'Invoice created and notification sent successfully');
        } catch (notificationErr: any) {
          console.error('Error sending invoice notification:', notificationErr);
          setError(notificationErr.response?.data?.error || 'Invoice saved but failed to send notification');
        }
      }

      // Upload file if one is selected
      if (file && createdInvoiceId) {
        try {
          // Validate file type before upload
          const allowedTypes = ['application/pdf', 'text/plain'];
          if (!allowedTypes.includes(file.type)) {
            throw new Error('Invalid file type. Only PDF and text files are allowed.');
          }

          // Validate file size (10MB limit)
          const maxSize = 10 * 1024 * 1024; // 10MB
          if (file.size > maxSize) {
            throw new Error('File size exceeds 10MB limit.');
          }

          console.log('Uploading file:', {
            name: file.name,
            type: file.type,
            size: file.size,
            invoiceId: createdInvoiceId
          });

          const uploadFormData = new FormData();
          uploadFormData.append('document', file);

          const response = await axios.post(
            `${API_URL}/invoices/${createdInvoiceId}/document`,
            uploadFormData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: `Bearer ${getAuthToken()}`
              },
              timeout: 60000, // 60 second timeout
              onUploadProgress: (progressEvent) => {
                if (progressEvent.total) {
                  const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                  console.log(`Upload progress: ${percentCompleted}%`);
                }
              }
            }
          );

          console.log('Document upload successful:', response.data);
          setFileUploadSuccess('Document uploaded successfully');
          // Update the main success message to include document upload
          setSuccess(prev =>
            prev ? `${prev} and document uploaded successfully` : 'Document uploaded successfully'
          );
        } catch (fileErr: any) {
          console.error('Error uploading document:', {
            message: fileErr.message,
            response: fileErr.response?.data,
            status: fileErr.response?.status,
            code: fileErr.code
          });

          let errorMessage = 'Failed to upload document';
          if (fileErr.response?.data?.error) {
            errorMessage = fileErr.response.data.error;
          } else if (fileErr.message) {
            errorMessage = fileErr.message;
          }

          setFileUploadError(errorMessage);
          // Update the main success message to include document upload failure
          setSuccess(prev =>
            prev ? `${prev}, but document upload failed` : 'Invoice saved, but document upload failed'
          );
          // Don't prevent navigation if file upload fails
        }
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/invoices');
      }, 2000);
    } catch (err: any) {
      console.error('Error saving invoice:', err);
      setError(err.response?.data?.error || 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Invoice' : 'Create New Invoice'}
        </h1>
        <button
          type="button"
          onClick={() => navigate('/invoices')}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {!selectedFarm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No farm selected</h3>
          <p className="text-gray-500 mb-6">Please select a farm from the header dropdown before creating invoices.</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-medium text-gray-900">Invoice Details</h2>
            </div>
            <div className="p-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
              {!isEditMode && (
                <div>
                  <label htmlFor="farmId" className="block text-sm font-medium text-gray-700 mb-1">
                    Farm <span className="text-red-500">*</span>
                  </label>
                  <div className="p-2 border rounded bg-gray-50">
                    {selectedFarm ? (
                      <span className="text-gray-700">{selectedFarm.name}</span>
                    ) : (
                      <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                    )}
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    To change the farm, use the farm selector in the header.
                  </p>
                  <input
                    type="hidden"
                    id="farmId"
                    name="farmId"
                    value={selectedFarm?.id || ''}
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Recipient Type <span className="text-red-500">*</span>
                </label>
                {isEditMode ? (
                  <div className="p-2 border rounded bg-gray-50">
                    <span className="text-gray-700">{formData.recipientType === 'customer' ? 'Customer' : 'Farm'}</span>
                  </div>
                ) : (
                  <div className="mt-1 flex space-x-4">
                    <div className="flex items-center">
                      <input
                        id="recipientTypeCustomer"
                        name="recipientType"
                        type="radio"
                        value="customer"
                        checked={formData.recipientType === 'customer'}
                        onChange={() => setFormData(prev => ({ ...prev, recipientType: 'customer', recipientFarmId: '' }))}
                        className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
                      />
                      <label htmlFor="recipientTypeCustomer" className="ml-2 block text-sm text-gray-700">
                        Customer
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="recipientTypeFarm"
                        name="recipientType"
                        type="radio"
                        value="farm"
                        checked={formData.recipientType === 'farm'}
                        onChange={() => setFormData(prev => ({ ...prev, recipientType: 'farm', customerId: '' }))}
                        className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
                      />
                      <label htmlFor="recipientTypeFarm" className="ml-2 block text-sm text-gray-700">
                        Farm
                      </label>
                    </div>
                  </div>
                )}
              </div>

              {formData.recipientType === 'customer' ? (
                <div>
                  <label htmlFor="customerId" className="block text-sm font-medium text-gray-700 mb-1">
                    Customer <span className="text-red-500">*</span>
                  </label>
                  {isEditMode ? (
                    <div className="p-2 border rounded bg-gray-50">
                      <span className="text-gray-700">
                        {customers.find(c => c.id === formData.customerId)?.name || 'Loading...'}
                      </span>
                    </div>
                  ) : (
                    <select
                      id="customerId"
                      name="customerId"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      value={formData.customerId}
                      onChange={handleChange}
                    >
                      <option value="">Select a customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>{customer.name}</option>
                      ))}
                    </select>
                  )}
                </div>
              ) : (
                <div>
                  <label htmlFor="recipientFarmId" className="block text-sm font-medium text-gray-700 mb-1">
                    Recipient Farm <span className="text-red-500">*</span>
                  </label>
                  {isEditMode ? (
                    <div className="p-2 border rounded bg-gray-50">
                      <span className="text-gray-700">
                        {farms.find(f => f.id === formData.recipientFarmId)?.name || 'Loading...'}
                      </span>
                    </div>
                  ) : (
                    <select
                      id="recipientFarmId"
                      name="recipientFarmId"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      value={formData.recipientFarmId}
                      onChange={handleChange}
                    >
                      <option value="">Select a farm</option>
                      {farms.map(farm => (
                        <option key={farm.id} value={farm.id}>{farm.name}</option>
                      ))}
                    </select>
                  )}
                  {!isEditMode && farms.length === 0 && (
                    <p className="mt-2 text-sm text-red-500">
                      No farms available with invoice permissions. You need to have active farm associations with invoice permissions enabled to create farm-to-farm invoices.
                    </p>
                  )}
                </div>
              )}

              <div>
                <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Invoice Number <span className="text-gray-500">(Auto-generated, can be customized)</span>
                </label>
                <input
                  type="text"
                  name="invoiceNumber"
                  id="invoiceNumber"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.invoiceNumber}
                  onChange={handleChange}
                  placeholder="Auto-generated (e.g., INV-001)"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Leave blank for automatic sequential numbering
                </p>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Issue Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="issueDate"
                  id="issueDate"
                  required
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.issueDate}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="dueDate"
                  id="dueDate"
                  required
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.dueDate}
                  onChange={handleChange}
                />
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  name="notes"
                  id="notes"
                  rows={3}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="Additional notes or payment instructions..."
                ></textarea>
              </div>

              {/* File upload (only in create mode) */}
              {!isEditMode && (
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Document Attachment
                  </label>
                  <div
                    className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md ${
                      dragActive ? 'bg-gray-50 border-primary-500' : ''
                    }`}
                    onDragEnter={handleDrag}
                    onDragOver={handleDrag}
                    onDragLeave={handleDrag}
                    onDrop={handleDrop}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-600 justify-center">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                        >
                          <span>Upload a file</span>
                          <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            accept=".pdf,.txt"
                            className="sr-only"
                            onChange={handleFileChange}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PDF and text files only
                      </p>
                      {file && (
                        <p className="text-sm text-primary-600">
                          Selected: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </p>
                      )}
                      {fileUploadError && (
                        <p className="text-sm text-red-600">{fileUploadError}</p>
                      )}
                    </div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    The document will be uploaded after the invoice is created.
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-medium text-gray-900">Email Notification</h2>
            </div>
            <div className="p-6">
              <div className="flex items-center mb-4">
                <input
                  id="sendNotification"
                  name="sendNotification"
                  type="checkbox"
                  checked={formData.sendNotification}
                  onChange={(e) => setFormData(prev => ({ ...prev, sendNotification: e.target.checked }))}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="sendNotification" className="ml-2 block text-sm text-gray-900">
                  Send invoice notification to customer
                </label>
              </div>

              {formData.sendNotification && (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="recipientEmail" className="block text-sm font-medium text-gray-700">
                      Recipient Emails
                    </label>

                    {/* Default email notice */}
                    {formData.recipientType === 'customer' && formData.customerId && (
                      <p className="mt-1 text-sm text-gray-500">
                        {customers.find(c => c.id === formData.customerId)?.email ? 
                          `Default: ${customers.find(c => c.id === formData.customerId)?.email} (will be used if no additional emails are added)` : 
                          'Enter email addresses to send the invoice to'}
                      </p>
                    )}

                    {/* Email input and add button */}
                    <div className="mt-2 flex">
                      <input
                        type="email"
                        name="newEmail"
                        id="newEmail"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addEmail()}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="Enter additional email address"
                      />
                      <button
                        type="button"
                        onClick={addEmail}
                        className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Add
                      </button>
                    </div>

                    {/* List of added emails */}
                    {formData.recipientEmails.length > 0 && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700">Recipients:</h4>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {formData.recipientEmails.map((email, index) => (
                            <div 
                              key={index} 
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {email}
                              <button
                                type="button"
                                onClick={() => removeEmail(email)}
                                className="ml-1.5 inline-flex text-blue-400 hover:text-blue-600 focus:outline-none"
                              >
                                <span className="sr-only">Remove</span>
                                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Invoice Items</h2>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setShowProductModal(true)}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Add Product
                </button>
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Add Item
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Taxable
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {formData.items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            required
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            placeholder="Item description"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            required
                            min="0.01"
                            step="0.01"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm text-right"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            required
                            min="0.01"
                            step="0.01"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm text-right"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', e.target.value)}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                          {formatCurrency(item.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={item.taxable !== false}
                            onChange={(e) => handleItemChange(index, 'taxable', e.target.checked)}
                            aria-label="Taxable"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            className="text-red-600 hover:text-red-900"
                            disabled={formData.items.length === 1}
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50">
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                        Subtotal
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                        {formatCurrency(calculateSubtotal())}
                      </td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                        Tax {selectedFarm?.tax_rate ? `(${selectedFarm.tax_rate}%)` : ''}
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                        {formatCurrency(calculateTax())}
                      </td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                        Total
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                        {formatCurrency(calculateTotal())}
                      </td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update Invoice' : 'Create Invoice'
              )}
            </button>
          </div>
        </form>
      )}
      {/* Product Selection Modal */}
      {showProductModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Select a Product
                    </h3>
                    {products.length === 0 ? (
                      <p className="text-sm text-gray-500">
                        No products available for this farm. Please add products first.
                      </p>
                    ) : (
                      <div className="max-h-60 overflow-y-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Name
                              </th>
                              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Price
                              </th>
                              <th scope="col" className="relative px-6 py-3">
                                <span className="sr-only">Add</span>
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {products.map((product) => (
                              <tr key={product.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {product.name}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                  {formatCurrency(product.price)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      addProductAsItem(product.id);
                                      setShowProductModal(false);
                                    }}
                                    className="text-primary-600 hover:text-primary-900"
                                  >
                                    Add
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setShowProductModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default InvoiceForm;
